import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Provider for the selected map style
final selectedMapStyleProvider = StateProvider<String>((ref) => 'standard');

/// A widget for selecting map styles
class MapStyleSelector extends ConsumerWidget {
  /// Callback when a style is selected
  final Function(String) onStyleSelected;

  /// Constructor
  const MapStyleSelector({
    super.key,
    required this.onStyleSelected,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedStyle = ref.watch(selectedMapStyleProvider);

    return PopupMenuButton<String>(
      icon: const Icon(Icons.layers),
      tooltip: 'Select map style',
      onSelected: (String style) {
        ref.read(selectedMapStyleProvider.notifier).state = style;
        onStyleSelected(style);
        _saveSelectedStyle(style);
      },
      itemBuilder: (BuildContext context) => <PopupMenuEntry<String>>[
        const PopupMenuItem<String>(
          value: 'standard',
          child: Text('Standard'),
        ),
        const PopupMenuItem<String>(
          value: 'silver',
          child: Text('Silver'),
        ),
        const PopupMenuItem<String>(
          value: 'retro',
          child: Text('Retro'),
        ),
        const PopupMenuItem<String>(
          value: 'dark',
          child: Text('Dark'),
        ),
        const PopupMenuItem<String>(
          value: 'night',
          child: Text('Night'),
        ),
        const PopupMenuItem<String>(
          value: 'aubergine',
          child: Text('Aubergine'),
        ),
      ],
    );
  }

  /// Save the selected style to shared preferences
  Future<void> _saveSelectedStyle(String style) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('map_style', style);
  }
}

/// A widget that loads the saved map style on initialization
class MapStyleInitializer extends ConsumerStatefulWidget {
  /// Child widget
  final Widget child;

  /// Constructor
  const MapStyleInitializer({
    super.key,
    required this.child,
  });

  @override
  ConsumerState<MapStyleInitializer> createState() =>
      _MapStyleInitializerState();
}

class _MapStyleInitializerState extends ConsumerState<MapStyleInitializer> {
  @override
  void initState() {
    super.initState();
    _loadSavedStyle();
  }

  /// Load the saved style from shared preferences
  Future<void> _loadSavedStyle() async {
    final prefs = await SharedPreferences.getInstance();
    final savedStyle = prefs.getString('map_style') ?? 'standard';

    // Update the provider with the saved style
    if (mounted) {
      ref.read(selectedMapStyleProvider.notifier).state = savedStyle;
    }
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}

/// A widget that displays a preview of a map style
class MapStylePreview extends StatelessWidget {
  /// The style name
  final String styleName;

  /// Whether this style is selected
  final bool isSelected;

  /// Callback when the style is tapped
  final VoidCallback onTap;

  /// Constructor
  const MapStylePreview({
    super.key,
    required this.styleName,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Get the preview color based on the style
    Color previewColor;
    switch (styleName) {
      case 'standard':
        previewColor = Colors.blue.shade100;
        break;
      case 'silver':
        previewColor = Colors.grey.shade300;
        break;
      case 'retro':
        previewColor = Colors.brown.shade200;
        break;
      case 'dark':
        previewColor = Colors.grey.shade800;
        break;
      case 'night':
        previewColor = Colors.indigo.shade900;
        break;
      case 'aubergine':
        previewColor = Colors.deepPurple.shade900;
        break;
      default:
        previewColor = Colors.blue.shade100;
    }

    // Get the text color based on the style
    Color textColor;
    switch (styleName) {
      case 'dark':
      case 'night':
      case 'aubergine':
        textColor = Colors.white;
        break;
      default:
        textColor = Colors.black87;
    }

    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 100,
        height: 70,
        margin: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: previewColor,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected ? theme.colorScheme.primary : Colors.transparent,
            width: 2,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(25),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              _getStyleDisplayName(styleName),
              style: TextStyle(
                color: textColor,
                fontWeight: FontWeight.bold,
              ),
            ),
            if (isSelected)
              Icon(
                Icons.check_circle,
                color: theme.colorScheme.primary,
                size: 16,
              ),
          ],
        ),
      ),
    );
  }

  /// Get a display name for the style
  String _getStyleDisplayName(String style) {
    switch (style) {
      case 'standard':
        return 'Standard';
      case 'silver':
        return 'Silver';
      case 'retro':
        return 'Retro';
      case 'dark':
        return 'Dark';
      case 'night':
        return 'Night';
      case 'aubergine':
        return 'Aubergine';
      default:
        return style;
    }
  }
}
