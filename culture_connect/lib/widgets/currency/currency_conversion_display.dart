import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:culture_connect/models/currency/currency_model.dart';
import 'package:culture_connect/models/currency/exchange_rate_model.dart';
import 'package:culture_connect/providers/currency/currency_providers.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// A widget for displaying a converted currency amount
///
/// This widget provides:
/// - Real-time currency conversion display
/// - Support for showing original amounts and exchange rates
/// - Customizable styling for different text elements
/// - Error handling and loading states
/// - Offline support with cached exchange rates
class CurrencyConversionDisplay extends ConsumerWidget {
  /// The amount to convert
  final double amount;

  /// The original currency code
  final String originalCurrencyCode;

  /// The target currency code (if null, uses the user's preferred currency)
  final String? targetCurrencyCode;

  /// Whether to show the original amount
  final bool showOriginalAmount;

  /// Whether to show the exchange rate
  final bool showExchangeRate;

  /// Whether to show the last updated time
  final bool showLastUpdated;

  /// Whether to show currency flags
  final bool showFlags;

  /// Whether to show currency codes
  final bool showCodes;

  /// Whether to show currency names
  final bool showNames;

  /// Style for the converted amount text
  final TextStyle? convertedAmountStyle;

  /// Style for the original amount text
  final TextStyle? originalAmountStyle;

  /// Style for the exchange rate text
  final TextStyle? exchangeRateStyle;

  /// Style for the last updated text
  final TextStyle? lastUpdatedStyle;

  /// Creates a new currency conversion display
  const CurrencyConversionDisplay({
    super.key,
    required this.amount,
    required this.originalCurrencyCode,
    this.targetCurrencyCode,
    this.showOriginalAmount = true,
    this.showExchangeRate = true,
    this.showLastUpdated = true,
    this.showFlags = true,
    this.showCodes = true,
    this.showNames = false,
    this.convertedAmountStyle,
    this.originalAmountStyle,
    this.exchangeRateStyle,
    this.lastUpdatedStyle,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Get the target currency (user's preferred currency if not specified)
    final String effectiveTargetCurrency;
    if (targetCurrencyCode != null) {
      effectiveTargetCurrency = targetCurrencyCode!;
    } else {
      final preferences = ref.watch(currentCurrencyPreferencesProvider);
      effectiveTargetCurrency = preferences.preferredCurrency;
    }

    // If the currencies are the same, just show the original amount
    if (originalCurrencyCode == effectiveTargetCurrency) {
      return _buildOriginalAmountOnly(context, ref);
    }

    // Get the exchange rate
    final exchangeRateAsync = ref.watch(
        exchangeRateProvider((originalCurrencyCode, effectiveTargetCurrency)));

    return exchangeRateAsync.when(
      data: (exchangeRate) =>
          _buildConversionDisplay(context, ref, exchangeRate),
      loading: () => _buildLoadingState(),
      error: (error, stackTrace) => _buildErrorState(error),
    );
  }

  /// Build the widget when there's an error loading the exchange rate
  Widget _buildErrorState(Object error) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Row(
          children: [
            Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 16,
            ),
            SizedBox(width: 4),
            Expanded(
              child: Text(
                'Error loading exchange rate',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.red,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Text(
          'Showing original amount: ${_formatAmount(amount, originalCurrencyCode)}',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  /// Build the widget when the original and target currencies are the same
  Widget _buildOriginalAmountOnly(BuildContext context, WidgetRef ref) {
    final currency = ref
        .watch(currencyDataServiceProvider)
        .getCurrencyByCode(originalCurrencyCode);

    return Text(
      currency?.formatAmount(amount) ??
          _formatAmount(amount, originalCurrencyCode),
      style: convertedAmountStyle ??
          const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
    );
  }

  /// Build the widget when loading the exchange rate
  Widget _buildLoadingState() {
    return Row(
      children: [
        const SizedBox(
          width: 16,
          height: 16,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
          ),
        ),
        const SizedBox(width: 8),
        Text(
          'Converting...',
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  /// Build the main conversion display
  Widget _buildConversionDisplay(
    BuildContext context,
    WidgetRef ref,
    ExchangeRateModel exchangeRate,
  ) {
    // Get the currency models
    final originalCurrency = ref
        .watch(currencyDataServiceProvider)
        .getCurrencyByCode(originalCurrencyCode);
    final targetCurrency = ref
        .watch(currencyDataServiceProvider)
        .getCurrencyByCode(exchangeRate.targetCurrency);

    // Calculate the converted amount
    final convertedAmount = exchangeRate.convert(amount);

    // Format the amounts
    final formattedOriginalAmount = originalCurrency?.formatAmount(amount) ??
        _formatAmount(amount, originalCurrencyCode);
    final formattedConvertedAmount =
        targetCurrency?.formatAmount(convertedAmount) ??
            _formatAmount(convertedAmount, exchangeRate.targetCurrency);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Converted amount
        Row(
          children: [
            if (showFlags && targetCurrency != null) ...[
              Text(
                targetCurrency.flag,
                style: const TextStyle(fontSize: 16),
              ),
              const SizedBox(width: 4),
            ],
            if (showCodes) ...[
              Text(
                targetCurrency?.code ?? exchangeRate.targetCurrency,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(width: 4),
            ],
            if (showNames && targetCurrency != null) ...[
              Text(
                targetCurrency.name,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(width: 4),
            ],
            Expanded(
              child: Text(
                formattedConvertedAmount,
                style: convertedAmountStyle ??
                    const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            // Cached indicator
            if (exchangeRate.isFromCache) ...[
              const SizedBox(width: 4),
              Icon(
                Icons.cloud_off,
                size: 14,
                color: Colors.amber[700],
              ),
            ],
          ],
        ),

        // Original amount
        if (showOriginalAmount) ...[
          const SizedBox(height: 4),
          Row(
            children: [
              if (showFlags && originalCurrency != null) ...[
                Text(
                  originalCurrency.flag,
                  style: const TextStyle(fontSize: 14),
                ),
                const SizedBox(width: 4),
              ],
              if (showCodes) ...[
                Text(
                  originalCurrency?.code ?? originalCurrencyCode,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(width: 4),
              ],
              if (showNames && originalCurrency != null) ...[
                Text(
                  originalCurrency.name,
                  style: TextStyle(
                    fontSize: 10,
                    color: Colors.grey[500],
                  ),
                ),
                const SizedBox(width: 4),
              ],
              Expanded(
                child: Text(
                  formattedOriginalAmount,
                  style: originalAmountStyle ??
                      TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                        decoration: TextDecoration.lineThrough,
                      ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ],

        // Exchange rate
        if (showExchangeRate) ...[
          const SizedBox(height: 4),
          Row(
            children: [
              Expanded(
                child: Text(
                  '1 ${originalCurrency?.code ?? originalCurrencyCode} = ${exchangeRate.rate.toStringAsFixed(4)} ${targetCurrency?.code ?? exchangeRate.targetCurrency}',
                  style: exchangeRateStyle ??
                      TextStyle(
                        fontSize: 10,
                        color: Colors.grey[500],
                      ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              const SizedBox(width: 4),
              Text(
                exchangeRate.isFromCache ? 'Cached' : 'Live',
                style: TextStyle(
                  fontSize: 9,
                  color: exchangeRate.isFromCache
                      ? Colors.amber[700]
                      : Colors.green[700],
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ],

        // Last updated
        if (showLastUpdated) ...[
          const SizedBox(height: 2),
          Text(
            'Updated ${exchangeRate.lastUpdated}',
            style: lastUpdatedStyle ??
                TextStyle(
                  fontSize: 9,
                  color: Colors.grey[500],
                ),
          ),
        ],
      ],
    );
  }

  /// Format an amount with a currency code
  String _formatAmount(double amount, String currencyCode) {
    return '$currencyCode ${amount.toStringAsFixed(2)}';
  }
}
