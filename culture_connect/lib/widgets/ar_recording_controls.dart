import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/services/ar_recording_service.dart';

/// A widget that displays recording controls for AR experiences.
class ARRecordingControls extends ConsumerStatefulWidget {
  /// Called when the recording state changes.
  final VoidCallback? onRecordingStateChanged;

  /// Called when the recording is shared.
  final VoidCallback? onRecordingShared;

  /// Called when the recording is saved.
  final VoidCallback? onRecordingSaved;

  /// Called when the recording is discarded.
  final VoidCallback? onRecordingDiscarded;

  /// Create a new AR recording controls widget.
  const ARRecordingControls({
    super.key,
    this.onRecordingStateChanged,
    this.onRecordingShared,
    this.onRecordingSaved,
    this.onRecordingDiscarded,
  });

  @override
  ConsumerState<ARRecordingControls> createState() =>
      _ARRecordingControlsState();
}

class _ARRecordingControlsState extends ConsumerState<ARRecordingControls> {
  late ARRecordingService _recordingService;
  int _recordingDuration = 0;
  bool _showShareDialog = false;

  // Controllers for share dialog
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  final TextEditingController _tagsController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _recordingService = ref.read(arRecordingServiceProvider);
    _recordingService.initialize(
      onRecordingStateChanged: _handleRecordingStateChanged,
      onRecordingTimerUpdated: _handleRecordingTimerUpdated,
    );
  }

  void _handleRecordingStateChanged() {
    setState(() {});
    widget.onRecordingStateChanged?.call();
  }

  void _handleRecordingTimerUpdated(int durationInSeconds) {
    setState(() {
      _recordingDuration = durationInSeconds;
    });
  }

  Future<void> _handleStartRecording() async {
    final success = await _recordingService.startRecording();
    if (!success) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to start recording'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }

  void _handlePauseRecording() {
    _recordingService.pauseRecording();
  }

  void _handleResumeRecording() {
    _recordingService.resumeRecording();
  }

  Future<void> _handleStopRecording() async {
    final success = await _recordingService.stopRecording();
    if (!success) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to stop recording'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    } else {
      setState(() {
        _showShareDialog = true;
      });
    }
  }

  Future<void> _handleTakeScreenshot() async {
    final screenshot = await _recordingService.takeScreenshot();
    if (screenshot == null) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to take screenshot'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Screenshot taken'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }

  Future<void> _handleRecordVideo() async {
    final video = await _recordingService.recordVideo();
    if (video == null) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to record video'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Video recorded'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }

  Future<void> _handleShareRecording() async {
    final title = _titleController.text.trim();
    final description = _descriptionController.text.trim();
    final tagsText = _tagsController.text.trim();
    final tags = tagsText.isNotEmpty
        ? tagsText.split(',').map((tag) => tag.trim()).toList()
        : null;

    if (title.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter a title'),
          duration: Duration(seconds: 2),
        ),
      );
      return;
    }

    final success = await _recordingService.shareRecording(
      title: title,
      description: description,
      tags: tags,
    );

    if (!success) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to share recording'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    } else {
      if (mounted) {
        setState(() {
          _showShareDialog = false;
        });

        widget.onRecordingShared?.call();

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Recording shared'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }

  Future<void> _handleSaveRecording() async {
    final title = _titleController.text.trim();
    final description = _descriptionController.text.trim();
    final tagsText = _tagsController.text.trim();
    final tags = tagsText.isNotEmpty
        ? tagsText.split(',').map((tag) => tag.trim()).toList()
        : null;

    if (title.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter a title'),
          duration: Duration(seconds: 2),
        ),
      );
      return;
    }

    final success = await _recordingService.saveRecording(
      title: title,
      description: description,
      tags: tags,
    );

    if (!success) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to save recording'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    } else {
      if (mounted) {
        setState(() {
          _showShareDialog = false;
        });

        widget.onRecordingSaved?.call();

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Recording saved'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }

  void _handleDiscardRecording() {
    setState(() {
      _showShareDialog = false;
    });

    widget.onRecordingDiscarded?.call();
  }

  @override
  Widget build(BuildContext context) {
    if (_showShareDialog) {
      return _buildShareDialog();
    }

    return _buildRecordingControls();
  }

  Widget _buildRecordingControls() {
    final isRecording = _recordingService.isRecording;
    final isPaused = _recordingService.isPaused;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withAlpha(230),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(51),
            blurRadius: 10,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                isRecording ? 'Recording' : 'AR Recording',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              if (isRecording)
                Row(
                  children: [
                    const Icon(Icons.fiber_manual_record,
                        color: Colors.red, size: 16),
                    const SizedBox(width: 4),
                    Text(
                      _recordingService.recordingDurationFormatted,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ],
                ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              if (!isRecording)
                _buildControlButton(
                  icon: Icons.fiber_manual_record,
                  label: 'Record',
                  color: Colors.red,
                  onPressed: _handleStartRecording,
                ),
              if (isRecording && !isPaused)
                _buildControlButton(
                  icon: Icons.pause,
                  label: 'Pause',
                  color: Colors.orange,
                  onPressed: _handlePauseRecording,
                ),
              if (isRecording && isPaused)
                _buildControlButton(
                  icon: Icons.play_arrow,
                  label: 'Resume',
                  color: Colors.green,
                  onPressed: _handleResumeRecording,
                ),
              if (isRecording)
                _buildControlButton(
                  icon: Icons.stop,
                  label: 'Stop',
                  color: Colors.red,
                  onPressed: _handleStopRecording,
                ),
              _buildControlButton(
                icon: Icons.camera_alt,
                label: 'Screenshot',
                color: Colors.blue,
                onPressed: _handleTakeScreenshot,
              ),
              _buildControlButton(
                icon: Icons.videocam,
                label: 'Video',
                color: Colors.purple,
                onPressed: _handleRecordVideo,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onPressed,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        IconButton(
          icon: Icon(icon),
          color: color,
          onPressed: onPressed,
          tooltip: label,
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall,
        ),
      ],
    );
  }

  Widget _buildShareDialog() {
    final screenshots = _recordingService.screenshots;
    final videoFile = _recordingService.videoFile;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(51),
            blurRadius: 10,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Share Your AR Experience',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _titleController,
            decoration: const InputDecoration(
              labelText: 'Title',
              border: OutlineInputBorder(),
            ),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _descriptionController,
            decoration: const InputDecoration(
              labelText: 'Description',
              border: OutlineInputBorder(),
            ),
            maxLines: 3,
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _tagsController,
            decoration: const InputDecoration(
              labelText: 'Tags (comma separated)',
              border: OutlineInputBorder(),
            ),
          ),
          const SizedBox(height: 16),
          if (screenshots.isNotEmpty || videoFile != null)
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Media',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const SizedBox(height: 8),
                SizedBox(
                  height: 80,
                  child: ListView(
                    scrollDirection: Axis.horizontal,
                    children: [
                      ...screenshots.map(
                          (file) => _buildMediaThumbnail(file, isVideo: false)),
                      if (videoFile != null)
                        _buildMediaThumbnail(videoFile, isVideo: true),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
              ],
            ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: _handleDiscardRecording,
                  child: const Text('Discard'),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton(
                  onPressed: _handleSaveRecording,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Save'),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton(
                  onPressed: _handleShareRecording,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).primaryColor,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Share'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMediaThumbnail(File file, {required bool isVideo}) {
    return Container(
      width: 80,
      height: 80,
      margin: const EdgeInsets.only(right: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        image: DecorationImage(
          image: FileImage(file),
          fit: BoxFit.cover,
        ),
      ),
      child: isVideo
          ? const Center(
              child: Icon(
                Icons.play_circle_fill,
                color: Colors.white,
                size: 32,
              ),
            )
          : null,
    );
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _tagsController.dispose();
    super.dispose();
  }
}
