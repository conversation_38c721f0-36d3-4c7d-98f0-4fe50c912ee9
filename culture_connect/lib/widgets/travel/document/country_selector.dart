import 'package:flutter/material.dart';
import 'package:culture_connect/models/common/country.dart';
import 'package:culture_connect/services/country_service.dart';
import 'package:culture_connect/theme/app_colors.dart';
import 'package:culture_connect/theme/app_text_styles.dart';

/// A widget for selecting a country
class CountrySelector extends StatefulWidget {
  /// The initially selected country code
  final String? initialCountryCode;

  /// Callback when a country is selected
  final Function(Country country) onCountrySelected;

  /// The label for the selector
  final String label;

  /// The hint text
  final String hint;

  /// Whether the field is required
  final bool isRequired;

  /// Creates a new country selector
  const CountrySelector({
    Key? key,
    this.initialCountryCode,
    required this.onCountrySelected,
    this.label = 'Country',
    this.hint = 'Select a country',
    this.isRequired = true,
  }) : super(key: key);

  @override
  State<CountrySelector> createState() => _CountrySelectorState();
}

class _CountrySelectorState extends State<CountrySelector> {
  final CountryService _countryService = CountryService();
  List<Country> _countries = [];
  Country? _selectedCountry;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadCountries();
  }

  /// Load the list of countries
  Future<void> _loadCountries() async {
    setState(() {
      _isLoading = true;
    });

    try {
      _countries = await _countryService.getCountries();

      if (widget.initialCountryCode != null) {
        _selectedCountry = _countries.firstWhere(
          (country) => country.code == widget.initialCountryCode,
          orElse: () => _countries.first,
        );
      }

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.label + (widget.isRequired ? ' *' : ''),
          style: AppTextStyles.subtitle2,
        ),
        const SizedBox(height: 8),
        _isLoading
            ? const LinearProgressIndicator()
            : InkWell(
                onTap: _showCountryPicker,
                borderRadius: BorderRadius.circular(8),
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  decoration: BoxDecoration(
                    border: Border.all(color: AppColors.border),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      if (_selectedCountry != null) ...[
                        ClipRRect(
                          borderRadius: BorderRadius.circular(4),
                          child: Image.asset(
                            'assets/flags/${_selectedCountry!.code.toLowerCase()}.png',
                            width: 24,
                            height: 16,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                width: 24,
                                height: 16,
                                color: AppColors.surface,
                                child: const Center(
                                  child: Icon(
                                    Icons.flag,
                                    size: 12,
                                    color: AppColors.textSecondary,
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            _selectedCountry!.name,
                            style: AppTextStyles.body1,
                          ),
                        ),
                      ] else
                        Expanded(
                          child: Text(
                            widget.hint,
                            style: AppTextStyles.body1.copyWith(
                              color: AppColors.textSecondary,
                            ),
                          ),
                        ),
                      const Icon(
                        Icons.arrow_drop_down,
                        color: AppColors.textSecondary,
                      ),
                    ],
                  ),
                ),
              ),
      ],
    );
  }

  /// Show the country picker dialog
  void _showCountryPicker() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return DraggableScrollableSheet(
          initialChildSize: 0.7,
          minChildSize: 0.5,
          maxChildSize: 0.9,
          expand: false,
          builder: (context, scrollController) {
            return Column(
              children: [
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      Expanded(
                        child: Text(
                          'Select a Country',
                          style: AppTextStyles.headline6,
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.close),
                        onPressed: () => Navigator.pop(context),
                      ),
                    ],
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: TextField(
                    decoration: const InputDecoration(
                      hintText: 'Search countries',
                      prefixIcon: Icon(Icons.search),
                      border: OutlineInputBorder(),
                    ),
                    onChanged: (value) {
                      // Filter countries based on search query
                      // This would be implemented in a real app
                    },
                  ),
                ),
                const SizedBox(height: 8),
                Expanded(
                  child: ListView.builder(
                    controller: scrollController,
                    itemCount: _countries.length,
                    itemBuilder: (context, index) {
                      final country = _countries[index];
                      return ListTile(
                        leading: ClipRRect(
                          borderRadius: BorderRadius.circular(4),
                          child: Image.asset(
                            'assets/flags/${country.code.toLowerCase()}.png',
                            width: 32,
                            height: 24,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                width: 32,
                                height: 24,
                                color: AppColors.surface,
                                child: const Center(
                                  child: Icon(
                                    Icons.flag,
                                    size: 16,
                                    color: AppColors.textSecondary,
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                        title: Text(country.name),
                        subtitle: Text(country.code),
                        selected: _selectedCountry?.code == country.code,
                        selectedTileColor: AppColors.primary.withAlpha(25),
                        onTap: () {
                          setState(() {
                            _selectedCountry = country;
                          });
                          widget.onCountrySelected(country);
                          Navigator.pop(context);
                        },
                      );
                    },
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }
}
