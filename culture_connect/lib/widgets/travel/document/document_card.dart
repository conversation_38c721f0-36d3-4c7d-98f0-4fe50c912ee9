import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:culture_connect/models/travel/document/travel_documents.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// A card widget for displaying a travel document
class DocumentCard extends StatelessWidget {
  /// The travel document to display
  final TravelDocument document;
  
  /// Whether to show the full details
  final bool showFullDetails;
  
  /// Callback when the card is tapped
  final VoidCallback? onTap;
  
  /// Callback when the edit button is tapped
  final VoidCallback? onEdit;
  
  /// Callback when the delete button is tapped
  final VoidCallback? onDelete;
  
  /// Creates a new document card
  const DocumentCard({
    super.key,
    required this.document,
    this.showFullDetails = false,
    this.onTap,
    this.onEdit,
    this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12.r),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                document.type.color.withAlpha(50),
                Colors.white,
              ],
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Container(
                padding: EdgeInsets.all(16.r),
                decoration: BoxDecoration(
                  color: document.type.color.withAlpha(30),
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(12.r),
                    topRight: Radius.circular(12.r),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      document.type.icon,
                      color: document.type.color,
                      size: 24.r,
                    ),
                    SizedBox(width: 8.w),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            document.type.displayName,
                            style: TextStyle(
                              fontSize: 16.sp,
                              fontWeight: FontWeight.bold,
                              color: document.type.color,
                            ),
                          ),
                          Text(
                            document.name,
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: Colors.grey[700],
                            ),
                          ),
                        ],
                      ),
                    ),
                    _buildStatusBadge(),
                  ],
                ),
              ),
              
              // Content
              Padding(
                padding: EdgeInsets.all(16.r),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Document number
                    Row(
                      children: [
                        Icon(
                          Icons.numbers,
                          size: 16.r,
                          color: Colors.grey[600],
                        ),
                        SizedBox(width: 8.w),
                        Text(
                          'Document Number:',
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: Colors.grey[600],
                          ),
                        ),
                        SizedBox(width: 4.w),
                        Text(
                          document.documentNumber,
                          style: TextStyle(
                            fontSize: 14.sp,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    
                    SizedBox(height: 8.h),
                    
                    // Issued by
                    Row(
                      children: [
                        Icon(
                          Icons.location_city,
                          size: 16.r,
                          color: Colors.grey[600],
                        ),
                        SizedBox(width: 8.w),
                        Text(
                          'Issued by:',
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: Colors.grey[600],
                          ),
                        ),
                        SizedBox(width: 4.w),
                        Text(
                          document.issuedBy,
                          style: TextStyle(
                            fontSize: 14.sp,
                          ),
                        ),
                      ],
                    ),
                    
                    SizedBox(height: 8.h),
                    
                    // Dates
                    Row(
                      children: [
                        Expanded(
                          child: Row(
                            children: [
                              Icon(
                                Icons.calendar_today,
                                size: 16.r,
                                color: Colors.grey[600],
                              ),
                              SizedBox(width: 8.w),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Issued:',
                                    style: TextStyle(
                                      fontSize: 12.sp,
                                      color: Colors.grey[600],
                                    ),
                                  ),
                                  Text(
                                    document.formattedIssuedDate,
                                    style: TextStyle(
                                      fontSize: 14.sp,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        Expanded(
                          child: Row(
                            children: [
                              Icon(
                                Icons.event,
                                size: 16.r,
                                color: document.isExpiringSoon
                                    ? Colors.orange
                                    : document.isExpired
                                        ? Colors.red
                                        : Colors.grey[600],
                              ),
                              SizedBox(width: 8.w),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Expires:',
                                    style: TextStyle(
                                      fontSize: 12.sp,
                                      color: Colors.grey[600],
                                    ),
                                  ),
                                  Text(
                                    document.formattedExpiryDate,
                                    style: TextStyle(
                                      fontSize: 14.sp,
                                      color: document.isExpiringSoon
                                          ? Colors.orange
                                          : document.isExpired
                                              ? Colors.red
                                              : null,
                                      fontWeight: document.isExpiringSoon || document.isExpired
                                          ? FontWeight.bold
                                          : null,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    
                    // Additional details for specific document types
                    if (showFullDetails) ...[
                      SizedBox(height: 16.h),
                      _buildAdditionalDetails(),
                    ],
                  ],
                ),
              ),
              
              // Actions
              if (onEdit != null || onDelete != null)
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      if (onEdit != null)
                        IconButton(
                          icon: const Icon(Icons.edit),
                          onPressed: onEdit,
                          tooltip: 'Edit',
                          color: AppTheme.primaryColor,
                        ),
                      if (onDelete != null)
                        IconButton(
                          icon: const Icon(Icons.delete),
                          onPressed: onDelete,
                          tooltip: 'Delete',
                          color: Colors.red,
                        ),
                    ],
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
  
  /// Build the status badge
  Widget _buildStatusBadge() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: document.status.color.withAlpha(50),
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            document.status.icon,
            color: document.status.color,
            size: 12.r,
          ),
          SizedBox(width: 4.w),
          Text(
            document.status.displayName,
            style: TextStyle(
              fontSize: 12.sp,
              fontWeight: FontWeight.bold,
              color: document.status.color,
            ),
          ),
        ],
      ),
    );
  }
  
  /// Build additional details based on document type
  Widget _buildAdditionalDetails() {
    if (document is Passport) {
      final passport = document as Passport;
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Divider(),
          SizedBox(height: 8.h),
          Text(
            'Passport Details',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 8.h),
          _buildDetailRow('Nationality', passport.nationality),
          SizedBox(height: 4.h),
          _buildDetailRow('Country Code', passport.countryCode),
          SizedBox(height: 4.h),
          _buildDetailRow('Place of Birth', passport.placeOfBirth),
          SizedBox(height: 4.h),
          _buildDetailRow('Date of Birth', passport.dateOfBirth.toString().split(' ')[0]),
          SizedBox(height: 4.h),
          _buildDetailRow('Gender', passport.gender),
        ],
      );
    } else if (document is Visa) {
      final visa = document as Visa;
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Divider(),
          SizedBox(height: 8.h),
          Text(
            'Visa Details',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 8.h),
          _buildDetailRow('Visa Type', visa.visaType.displayName),
          SizedBox(height: 4.h),
          _buildDetailRow('Entry Type', visa.entryType.displayName),
          SizedBox(height: 4.h),
          _buildDetailRow('Country of Issue', visa.countryOfIssue),
          SizedBox(height: 4.h),
          _buildDetailRow('Valid For', visa.countryValidFor),
          SizedBox(height: 4.h),
          _buildDetailRow('Max Stay', '${visa.maxStayDuration} days'),
          if (visa.processingTime != null) ...[
            SizedBox(height: 4.h),
            _buildDetailRow('Processing Time', '${visa.processingTime} days'),
          ],
          if (visa.applicationReference != null) ...[
            SizedBox(height: 4.h),
            _buildDetailRow('Reference', visa.applicationReference!),
          ],
        ],
      );
    }
    
    return const SizedBox.shrink();
  }
  
  /// Build a detail row with label and value
  Widget _buildDetailRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 120.w,
          child: Text(
            '$label:',
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.grey[600],
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }
}
