import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:culture_connect/models/travel/transfer/transfers.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// A card widget for displaying a transfer booking
class TransferBookingCard extends StatelessWidget {
  /// The transfer booking to display
  final TransferBooking booking;
  
  /// Whether to show the full details
  final bool showFullDetails;
  
  /// Callback when the card is tapped
  final VoidCallback? onTap;
  
  /// Callback when the cancel button is tapped
  final VoidCallback? onCancel;
  
  /// Callback when the track button is tapped
  final VoidCallback? onTrack;
  
  /// Creates a new transfer booking card
  const TransferBookingCard({
    super.key,
    required this.booking,
    this.showFullDetails = false,
    this.onTap,
    this.onCancel,
    this.onTrack,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Container(
              padding: EdgeInsets.all(16.r),
              decoration: BoxDecoration(
                color: booking.status.color.withAlpha(30),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(12.r),
                  topRight: Radius.circular(12.r),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    booking.status.icon,
                    color: booking.status.color,
                    size: 24.r,
                  ),
                  SizedBox(width: 8.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Airport Transfer',
                          style: TextStyle(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          'Booking #${booking.id.substring(0, 8)}',
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: Colors.grey[700],
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                    decoration: BoxDecoration(
                      color: booking.status.color.withAlpha(50),
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    child: Text(
                      booking.status.displayName,
                      style: TextStyle(
                        fontSize: 12.sp,
                        fontWeight: FontWeight.bold,
                        color: booking.status.color,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            
            // Content
            Padding(
              padding: EdgeInsets.all(16.r),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Date and time
                  Row(
                    children: [
                      Icon(
                        Icons.event,
                        size: 16.r,
                        color: Colors.grey[600],
                      ),
                      SizedBox(width: 8.w),
                      Text(
                        'Date:',
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: Colors.grey[600],
                        ),
                      ),
                      SizedBox(width: 4.w),
                      Text(
                        _formatDate(booking.pickupDateTime),
                        style: TextStyle(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(width: 16.w),
                      Icon(
                        Icons.access_time,
                        size: 16.r,
                        color: Colors.grey[600],
                      ),
                      SizedBox(width: 8.w),
                      Text(
                        'Time:',
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: Colors.grey[600],
                        ),
                      ),
                      SizedBox(width: 4.w),
                      Text(
                        _formatTime(booking.pickupDateTime),
                        style: TextStyle(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  
                  SizedBox(height: 16.h),
                  
                  // Pickup and dropoff
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Column(
                        children: [
                          Icon(
                            Icons.circle,
                            size: 16.r,
                            color: Colors.green,
                          ),
                          Container(
                            width: 2.w,
                            height: 30.h,
                            color: Colors.grey[300],
                          ),
                          Icon(
                            Icons.location_on,
                            size: 16.r,
                            color: Colors.red,
                          ),
                        ],
                      ),
                      SizedBox(width: 8.w),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Pickup',
                              style: TextStyle(
                                fontSize: 12.sp,
                                color: Colors.grey[600],
                              ),
                            ),
                            Text(
                              booking.pickupLocation.name,
                              style: TextStyle(
                                fontSize: 14.sp,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              booking.pickupLocation.address,
                              style: TextStyle(
                                fontSize: 12.sp,
                                color: Colors.grey[600],
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                            SizedBox(height: 16.h),
                            Text(
                              'Dropoff',
                              style: TextStyle(
                                fontSize: 12.sp,
                                color: Colors.grey[600],
                              ),
                            ),
                            Text(
                              booking.dropoffLocation.name,
                              style: TextStyle(
                                fontSize: 14.sp,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              booking.dropoffLocation.address,
                              style: TextStyle(
                                fontSize: 12.sp,
                                color: Colors.grey[600],
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  
                  SizedBox(height: 16.h),
                  
                  // Vehicle and passengers
                  Row(
                    children: [
                      if (booking.transferService != null) ...[
                        Icon(
                          booking.transferService!.vehicleType.icon,
                          size: 16.r,
                          color: booking.transferService!.vehicleType.color,
                        ),
                        SizedBox(width: 4.w),
                        Text(
                          booking.transferService!.vehicleType.displayName,
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: booking.transferService!.vehicleType.color,
                          ),
                        ),
                        SizedBox(width: 16.w),
                      ],
                      Icon(
                        Icons.person,
                        size: 16.r,
                        color: Colors.grey[600],
                      ),
                      SizedBox(width: 4.w),
                      Text(
                        '${booking.passengerCount} passengers',
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: Colors.grey[600],
                        ),
                      ),
                      SizedBox(width: 16.w),
                      Icon(
                        Icons.luggage,
                        size: 16.r,
                        color: Colors.grey[600],
                      ),
                      SizedBox(width: 4.w),
                      Text(
                        '${booking.luggageCount} luggage',
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                  
                  // Additional details
                  if (showFullDetails) ...[
                    SizedBox(height: 16.h),
                    Divider(),
                    SizedBox(height: 16.h),
                    _buildContactDetails(),
                    if (booking.flightInfo != null) ...[
                      SizedBox(height: 16.h),
                      _buildFlightDetails(),
                    ],
                    if (booking.driverName != null) ...[
                      SizedBox(height: 16.h),
                      _buildDriverDetails(),
                    ],
                    SizedBox(height: 16.h),
                    _buildPaymentDetails(),
                  ],
                  
                  SizedBox(height: 16.h),
                  
                  // Price and actions
                  Row(
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Total Price',
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: Colors.grey[600],
                            ),
                          ),
                          Text(
                            '${booking.currency} ${booking.totalPrice.toStringAsFixed(2)}',
                            style: TextStyle(
                              fontSize: 18.sp,
                              fontWeight: FontWeight.bold,
                              color: AppTheme.primaryColor,
                            ),
                          ),
                        ],
                      ),
                      const Spacer(),
                      if (booking.status == TransferBookingStatus.confirmed ||
                          booking.status == TransferBookingStatus.pending) ...[
                        if (onCancel != null)
                          OutlinedButton(
                            onPressed: onCancel,
                            style: OutlinedButton.styleFrom(
                              foregroundColor: Colors.red,
                              side: const BorderSide(color: Colors.red),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8.r),
                              ),
                              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                            ),
                            child: const Text('Cancel'),
                          ),
                        SizedBox(width: 8.w),
                      ],
                      if (booking.status == TransferBookingStatus.confirmed ||
                          booking.status == TransferBookingStatus.inProgress) ...[
                        if (onTrack != null)
                          ElevatedButton(
                            onPressed: onTrack,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppTheme.primaryColor,
                              foregroundColor: Colors.white,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8.r),
                              ),
                              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                            ),
                            child: const Text('Track'),
                          ),
                      ],
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  /// Build contact details section
  Widget _buildContactDetails() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Contact Details',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 8.h),
        _buildDetailRow('Name', booking.contactName),
        SizedBox(height: 4.h),
        _buildDetailRow('Phone', booking.contactPhone),
        SizedBox(height: 4.h),
        _buildDetailRow('Email', booking.contactEmail),
      ],
    );
  }
  
  /// Build flight details section
  Widget _buildFlightDetails() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Flight Information',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 8.h),
        Container(
          padding: EdgeInsets.all(12.r),
          decoration: BoxDecoration(
            color: Colors.blue[50],
            borderRadius: BorderRadius.circular(8.r),
            border: Border.all(color: Colors.blue[200]!),
          ),
          child: Row(
            children: [
              Icon(
                Icons.flight,
                color: Colors.blue,
                size: 20.r,
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: Text(
                  booking.flightInfo!,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.blue[800],
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
  
  /// Build driver details section
  Widget _buildDriverDetails() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Driver Information',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 8.h),
        _buildDetailRow('Driver', booking.driverName!),
        if (booking.driverPhone != null) ...[
          SizedBox(height: 4.h),
          _buildDetailRow('Phone', booking.driverPhone!),
        ],
        if (booking.vehicleDetails != null) ...[
          SizedBox(height: 4.h),
          _buildDetailRow('Vehicle', booking.vehicleDetails!),
        ],
        if (booking.vehicleLicensePlate != null) ...[
          SizedBox(height: 4.h),
          _buildDetailRow('License Plate', booking.vehicleLicensePlate!),
        ],
      ],
    );
  }
  
  /// Build payment details section
  Widget _buildPaymentDetails() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Payment Details',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 8.h),
        _buildDetailRow('Total Price', '${booking.currency} ${booking.totalPrice.toStringAsFixed(2)}'),
        if (booking.paymentMethod != null) ...[
          SizedBox(height: 4.h),
          _buildDetailRow('Payment Method', booking.paymentMethod!),
        ],
        if (booking.confirmationCode != null) ...[
          SizedBox(height: 4.h),
          _buildDetailRow('Confirmation Code', booking.confirmationCode!),
        ],
        if (booking.status == TransferBookingStatus.cancelled && booking.refundAmount != null) ...[
          SizedBox(height: 4.h),
          _buildDetailRow('Refund Amount', '${booking.currency} ${booking.refundAmount!.toStringAsFixed(2)}'),
          if (booking.refundDate != null) ...[
            SizedBox(height: 4.h),
            _buildDetailRow('Refund Date', _formatDate(booking.refundDate!)),
          ],
        ],
      ],
    );
  }
  
  /// Build a detail row with label and value
  Widget _buildDetailRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 100.w,
          child: Text(
            '$label:',
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.grey[600],
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }
  
  /// Format a date
  String _formatDate(DateTime date) {
    return '${date.month}/${date.day}/${date.year}';
  }
  
  /// Format a time
  String _formatTime(DateTime date) {
    final hour = date.hour > 12 ? date.hour - 12 : date.hour == 0 ? 12 : date.hour;
    final minute = date.minute.toString().padLeft(2, '0');
    final period = date.hour >= 12 ? 'PM' : 'AM';
    return '$hour:$minute $period';
  }
}
