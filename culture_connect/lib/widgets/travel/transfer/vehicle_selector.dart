import 'package:flutter/material.dart';
import 'package:culture_connect/models/travel/transfer/transfer_vehicle.dart';
import 'package:culture_connect/theme/app_colors.dart';
import 'package:culture_connect/theme/app_text_styles.dart';

/// A widget for selecting a vehicle type
class VehicleSelector extends StatefulWidget {
  /// The initially selected vehicle type
  final TransferVehicleType? initialVehicleType;

  /// Callback when a vehicle type is selected
  final Function(TransferVehicleType vehicleType) onVehicleSelected;

  /// Creates a new vehicle selector
  const VehicleSelector({
    Key? key,
    this.initialVehicleType,
    required this.onVehicleSelected,
  }) : super(key: key);

  @override
  State<VehicleSelector> createState() => _VehicleSelectorState();
}

class _VehicleSelectorState extends State<VehicleSelector> {
  TransferVehicleType? _selectedVehicleType;

  @override
  void initState() {
    super.initState();
    _selectedVehicleType = widget.initialVehicleType;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Vehicle Type',
          style: AppTextStyles.subtitle1.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 120,
          child: ListView(
            scrollDirection: Axis.horizontal,
            children: TransferVehicleType.values.map((type) {
              return _buildVehicleTypeCard(type);
            }).toList(),
          ),
        ),
      ],
    );
  }

  /// Build a vehicle type card
  Widget _buildVehicleTypeCard(TransferVehicleType type) {
    final isSelected = _selectedVehicleType == type;

    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedVehicleType = type;
        });
        widget.onVehicleSelected(type);
      },
      child: Container(
        width: 100,
        margin: const EdgeInsets.only(right: 12),
        decoration: BoxDecoration(
          color: isSelected
              ? AppColors.primary.withAlpha(25)
              : AppColors
                  .surface, // withAlpha(25) is equivalent to withOpacity(0.1)
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? AppColors.primary : AppColors.border,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: isSelected
                    ? AppColors.primary.withAlpha(
                        51) // withAlpha(51) is equivalent to withOpacity(0.2)
                    : AppColors.surface,
                shape: BoxShape.circle,
              ),
              child: Icon(
                _getVehicleIcon(type),
                color: isSelected ? AppColors.primary : AppColors.textSecondary,
                size: 32,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              type.displayName,
              style: AppTextStyles.caption.copyWith(
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                color: isSelected ? AppColors.primary : AppColors.textPrimary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              '${type.typicalPassengerCapacity} passengers',
              style: AppTextStyles.caption.copyWith(
                color: AppColors.textSecondary,
                fontSize: 10,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// Get the icon for a vehicle type
  IconData _getVehicleIcon(TransferVehicleType type) {
    return type.icon;
  }
}
