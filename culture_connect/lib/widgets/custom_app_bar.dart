import 'package:flutter/material.dart';
import 'package:culture_connect/theme/app_theme.dart';

class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final String? subTitle;
  final List<Widget>? actions;
  final bool showBackButton;
  final Widget? leading;
  final bool centerTitle;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final double elevation;
  final Widget? flexibleSpace;
  final PreferredSizeWidget? bottom;
  final VoidCallback? onBackPressed;

  const CustomAppBar({
    super.key,
    required this.title,
    this.subTitle,
    this.actions,
    this.showBackButton = true,
    this.leading,
    this.centerTitle = true,
    this.backgroundColor,
    this.foregroundColor,
    this.elevation = 0,
    this.flexibleSpace,
    this.bottom,
    this.onBackPressed,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: subTitle != null
          ? Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    color: foregroundColor ?? AppTheme.textPrimaryColor,
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  subTitle!,
                  style: TextStyle(
                    color: (foregroundColor ?? AppTheme.textPrimaryColor)
                        .withOpacity(0.7),
                    fontSize: 14,
                    fontWeight: FontWeight.normal,
                  ),
                ),
              ],
            )
          : Text(
              title,
              style: TextStyle(
                color: foregroundColor ?? AppTheme.textPrimaryColor,
                fontSize: 20,
                fontWeight: FontWeight.w600,
              ),
            ),
      centerTitle: centerTitle,
      backgroundColor: backgroundColor ?? AppTheme.backgroundColor,
      foregroundColor: foregroundColor ?? AppTheme.textPrimaryColor,
      elevation: elevation,
      leading: _buildLeading(context),
      actions: actions,
      flexibleSpace: flexibleSpace,
      bottom: bottom,
    );
  }

  Widget? _buildLeading(BuildContext context) {
    if (leading != null) {
      return leading;
    }

    if (showBackButton) {
      return IconButton(
        icon: const Icon(Icons.arrow_back_ios, size: 20),
        onPressed: onBackPressed ?? () => Navigator.of(context).pop(),
      );
    }

    return null;
  }

  @override
  Size get preferredSize => Size.fromHeight(bottom != null
      ? kToolbarHeight + bottom!.preferredSize.height
      : kToolbarHeight);
}

class TransparentAppBar extends CustomAppBar {
  const TransparentAppBar({
    super.key,
    super.title = '',
    super.actions,
    super.showBackButton,
    super.leading,
    super.onBackPressed,
  }) : super(
          backgroundColor: Colors.transparent,
          foregroundColor: Colors.white,
          elevation: 0,
        );
}

class GradientAppBar extends CustomAppBar {
  GradientAppBar({
    super.key,
    required super.title,
    super.actions,
    super.showBackButton,
    super.leading,
    super.centerTitle,
    super.bottom,
    super.onBackPressed,
  }) : super(
          backgroundColor: Colors.transparent,
          foregroundColor: Colors.white,
          elevation: 0,
          flexibleSpace: Container(
            decoration: const BoxDecoration(
              gradient: AppTheme.primaryGradient,
            ),
          ),
        );
}

class SearchAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String hintText;
  final Function(String)? onChanged;
  final VoidCallback? onBackPressed;
  final VoidCallback? onFilterPressed;
  final TextEditingController? controller;
  final FocusNode? focusNode;

  const SearchAppBar({
    super.key,
    this.hintText = 'Search...',
    this.onChanged,
    this.onBackPressed,
    this.onFilterPressed,
    this.controller,
    this.focusNode,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      backgroundColor: AppTheme.backgroundColor,
      elevation: 0,
      titleSpacing: 0,
      leading: IconButton(
        icon: const Icon(Icons.arrow_back_ios, size: 20),
        color: AppTheme.textPrimaryColor,
        onPressed: onBackPressed ?? () => Navigator.of(context).pop(),
      ),
      title: Container(
        height: 40,
        margin: const EdgeInsets.only(right: 16),
        decoration: BoxDecoration(
          color: AppTheme.surfaceColor,
          borderRadius: BorderRadius.circular(20),
        ),
        child: TextField(
          controller: controller,
          focusNode: focusNode,
          onChanged: onChanged,
          decoration: InputDecoration(
            hintText: hintText,
            hintStyle: const TextStyle(
              color: AppTheme.textSecondaryColor,
              fontSize: 14,
            ),
            prefixIcon: const Icon(
              Icons.search,
              color: AppTheme.textSecondaryColor,
              size: 20,
            ),
            suffixIcon: onFilterPressed != null
                ? IconButton(
                    icon: const Icon(
                      Icons.filter_list,
                      color: AppTheme.textSecondaryColor,
                      size: 20,
                    ),
                    onPressed: onFilterPressed,
                  )
                : null,
            border: InputBorder.none,
            contentPadding: const EdgeInsets.symmetric(vertical: 10),
          ),
          style: const TextStyle(
            color: AppTheme.textPrimaryColor,
            fontSize: 14,
          ),
          textAlignVertical: TextAlignVertical.center,
        ),
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
