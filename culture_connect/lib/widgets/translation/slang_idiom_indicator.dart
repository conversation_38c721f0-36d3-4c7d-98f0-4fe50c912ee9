import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:culture_connect/models/translation/slang_idiom_model.dart';
import 'package:culture_connect/models/translation/translation_slang_idiom.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// A widget for displaying a slang and idiom indicator
class SlangIdiomIndicator extends StatelessWidget {
  /// The slang and idiom information
  final TranslationSlangIdiom slangIdiom;
  
  /// Whether to show the label
  final bool showLabel;
  
  /// Whether to use a compact layout
  final bool compact;
  
  /// Whether to use light colors (for dark backgrounds)
  final bool useLight;
  
  /// Callback when the indicator is tapped
  final VoidCallback? onTap;
  
  /// Creates a new slang and idiom indicator
  const SlangIdiomIndicator({
    super.key,
    required this.slangIdiom,
    this.showLabel = true,
    this.compact = false,
    this.useLight = false,
    this.onTap,
  });
  
  @override
  Widget build(BuildContext context) {
    if (!slangIdiom.hasExpressions) {
      return const SizedBox.shrink();
    }
    
    return GestureDetector(
      onTap: onTap,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Icon
          Icon(
            Icons.format_quote,
            size: compact ? 14.r : 18.r,
            color: useLight ? Colors.white70 : Colors.purple,
          ),
          
          if (showLabel) ...[
            SizedBox(width: 4.w),
            
            // Label
            Text(
              compact ? 'Slang/Idiom' : 'Slang & Idiom',
              style: TextStyle(
                fontSize: compact ? 10.sp : 12.sp,
                color: useLight
                    ? Colors.white70
                    : AppTheme.textSecondaryColor,
              ),
            ),
          ],
          
          // Badge with count
          if (slangIdiom.expressionCount > 0) ...[
            SizedBox(width: 4.w),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 4.w),
              decoration: BoxDecoration(
                color: useLight
                    ? Colors.white24
                    : Colors.purple.withOpacity(0.2),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Text(
                slangIdiom.expressionCount.toString(),
                style: TextStyle(
                  fontSize: compact ? 8.sp : 10.sp,
                  fontWeight: FontWeight.bold,
                  color: useLight
                      ? Colors.white
                      : Colors.purple,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }
}

/// A widget for displaying a slang or idiom expression
class SlangIdiomExpressionCard extends StatelessWidget {
  /// The expression
  final SlangIdiomExpression expression;
  
  /// Creates a new slang or idiom expression card
  const SlangIdiomExpressionCard({
    super.key,
    required this.expression,
  });
  
  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      margin: EdgeInsets.only(bottom: 12.h),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
        side: BorderSide(
          color: expression.type.color.withOpacity(0.3),
          width: 1.w,
        ),
      ),
      child: Padding(
        padding: EdgeInsets.all(12.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  expression.type.icon,
                  size: 20.r,
                  color: expression.type.color,
                ),
                SizedBox(width: 8.w),
                Text(
                  expression.type.displayName,
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                if (expression.isPotentiallyOffensive) ...[
                  const Spacer(),
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 8.w,
                      vertical: 2.h,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.red.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.warning_amber_outlined,
                          size: 14.r,
                          color: Colors.red,
                        ),
                        SizedBox(width: 4.w),
                        Text(
                          'Potentially Offensive',
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: Colors.red,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
            
            SizedBox(height: 8.h),
            
            // Expression
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(8.r),
              decoration: BoxDecoration(
                color: expression.type.color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Text(
                '"${expression.expression}"',
                style: TextStyle(
                  fontSize: 14.sp,
                  fontStyle: FontStyle.italic,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
            ),
            
            SizedBox(height: 12.h),
            
            // Meaning
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(
                  Icons.text_fields,
                  size: 16.r,
                  color: Colors.grey,
                ),
                SizedBox(width: 8.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Literal Meaning:',
                        style: TextStyle(
                          fontSize: 12.sp,
                          fontWeight: FontWeight.w500,
                          color: AppTheme.textSecondaryColor,
                        ),
                      ),
                      SizedBox(height: 2.h),
                      Text(
                        expression.literalMeaning,
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: AppTheme.textPrimaryColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 8.h),
            
            // Actual meaning
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(
                  Icons.lightbulb_outline,
                  size: 16.r,
                  color: Colors.amber,
                ),
                SizedBox(width: 8.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Actual Meaning:',
                        style: TextStyle(
                          fontSize: 12.sp,
                          fontWeight: FontWeight.w500,
                          color: AppTheme.textSecondaryColor,
                        ),
                      ),
                      SizedBox(height: 2.h),
                      Text(
                        expression.actualMeaning,
                        style: TextStyle(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w500,
                          color: AppTheme.textPrimaryColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            // Examples (if available)
            if (expression.examples != null && expression.examples!.isNotEmpty) ...[
              SizedBox(height: 12.h),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(
                    Icons.format_list_bulleted,
                    size: 16.r,
                    color: Colors.grey,
                  ),
                  SizedBox(width: 8.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Examples:',
                          style: TextStyle(
                            fontSize: 12.sp,
                            fontWeight: FontWeight.w500,
                            color: AppTheme.textSecondaryColor,
                          ),
                        ),
                        SizedBox(height: 4.h),
                        ...expression.examples!.map((example) {
                          return Padding(
                            padding: EdgeInsets.only(bottom: 4.h),
                            child: Text(
                              '• $example',
                              style: TextStyle(
                                fontSize: 14.sp,
                                fontStyle: FontStyle.italic,
                                color: AppTheme.textPrimaryColor,
                              ),
                            ),
                          );
                        }).toList(),
                      ],
                    ),
                  ),
                ],
              ),
            ],
            
            // Standard alternatives (if available)
            if (expression.standardAlternatives != null && expression.standardAlternatives!.isNotEmpty) ...[
              SizedBox(height: 12.h),
              Text(
                'Standard Alternatives:',
                style: TextStyle(
                  fontSize: 12.sp,
                  fontWeight: FontWeight.w500,
                  color: AppTheme.textSecondaryColor,
                ),
              ),
              SizedBox(height: 4.h),
              Wrap(
                spacing: 8.w,
                runSpacing: 8.h,
                children: expression.standardAlternatives!.map((alternative) {
                  return Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 8.w,
                      vertical: 4.h,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.blue.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8.r),
                      border: Border.all(
                        color: Colors.blue.withOpacity(0.3),
                        width: 1.w,
                      ),
                    ),
                    child: Text(
                      alternative,
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: Colors.blue,
                      ),
                    ),
                  );
                }).toList(),
              ),
            ],
            
            // Additional information
            if (expression.region != null || expression.origin != null) ...[
              SizedBox(height: 12.h),
              Wrap(
                spacing: 8.w,
                runSpacing: 8.h,
                children: [
                  if (expression.region != null)
                    _buildInfoChip(
                      Icons.location_on_outlined,
                      expression.region!,
                      Colors.green,
                    ),
                  if (expression.origin != null)
                    _buildInfoChip(
                      Icons.history,
                      'Origin: ${expression.origin!}',
                      Colors.brown,
                    ),
                  _buildInfoChip(
                    Icons.person_outline,
                    expression.formalityLevel.displayName,
                    expression.formalityLevel.color,
                  ),
                ],
              ),
            ],
            
            // Notes (if available)
            if (expression.notes != null) ...[
              SizedBox(height: 12.h),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(
                    Icons.note,
                    size: 16.r,
                    color: Colors.grey,
                  ),
                  SizedBox(width: 8.w),
                  Expanded(
                    child: Text(
                      expression.notes!,
                      style: TextStyle(
                        fontSize: 12.sp,
                        fontStyle: FontStyle.italic,
                        color: AppTheme.textSecondaryColor,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }
  
  /// Build an info chip
  Widget _buildInfoChip(IconData icon, String label, Color color) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: 8.w,
        vertical: 4.h,
      ),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 14.r,
            color: color,
          ),
          SizedBox(width: 4.w),
          Text(
            label,
            style: TextStyle(
              fontSize: 12.sp,
              color: color,
            ),
          ),
        ],
      ),
    );
  }
}
