import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:culture_connect/models/translation/image_text_translation_model.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// A widget that displays recognized text blocks over an image
class RecognizedTextOverlay extends StatefulWidget {
  /// The recognized text blocks
  final List<RecognizedTextBlock> textBlocks;
  
  /// The size of the image
  final Size imageSize;
  
  /// Whether to show the overlay
  final bool showOverlay;
  
  /// Callback when a text block is tapped
  final Function(RecognizedTextBlock)? onTextBlockTap;
  
  /// Creates a new recognized text overlay
  const RecognizedTextOverlay({
    Key? key,
    required this.textBlocks,
    required this.imageSize,
    this.showOverlay = true,
    this.onTextBlockTap,
  }) : super(key: key);
  
  @override
  State<RecognizedTextOverlay> createState() => _RecognizedTextOverlayState();
}

class _RecognizedTextOverlayState extends State<RecognizedTextOverlay> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _opacityAnimation;
  
  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    
    _opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 0.7,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    if (widget.showOverlay) {
      _animationController.forward();
    }
  }
  
  @override
  void didUpdateWidget(RecognizedTextOverlay oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.showOverlay != oldWidget.showOverlay) {
      if (widget.showOverlay) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    }
  }
  
  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Stack(
          children: widget.textBlocks.map((block) {
            // Convert normalized coordinates to actual coordinates
            final rect = Rect.fromLTRB(
              block.boundingBox.left * widget.imageSize.width,
              block.boundingBox.top * widget.imageSize.height,
              block.boundingBox.right * widget.imageSize.width,
              block.boundingBox.bottom * widget.imageSize.height,
            );
            
            return Positioned(
              left: rect.left,
              top: rect.top,
              width: rect.width,
              height: rect.height,
              child: GestureDetector(
                onTap: () {
                  if (widget.onTextBlockTap != null) {
                    widget.onTextBlockTap!(block);
                  }
                },
                child: Container(
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor.withOpacity(_opacityAnimation.value * 0.3),
                    border: Border.all(
                      color: AppTheme.primaryColor.withOpacity(_opacityAnimation.value),
                      width: 2.0,
                    ),
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                  child: Center(
                    child: Opacity(
                      opacity: _opacityAnimation.value,
                      child: Text(
                        block.languageCode != null ? '${block.languageCode!.toUpperCase()}' : '',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 10.sp,
                          fontWeight: FontWeight.bold,
                          shadows: [
                            Shadow(
                              color: Colors.black.withOpacity(0.5),
                              blurRadius: 2,
                              offset: const Offset(1, 1),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            );
          }).toList(),
        );
      },
    );
  }
}
