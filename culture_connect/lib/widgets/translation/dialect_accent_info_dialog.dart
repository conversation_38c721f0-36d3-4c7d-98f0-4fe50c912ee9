import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:culture_connect/models/translation/accent_model.dart';
import 'package:culture_connect/models/translation/dialect_model.dart';
import 'package:culture_connect/services/voice_translation/dialect_accent_detection_service.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// A dialog that displays detailed dialect and accent information
class DialectAccentInfoDialog extends ConsumerStatefulWidget {
  /// The dialect to display
  final DialectModel? dialect;

  /// The accent to display
  final AccentModel? accent;

  /// The confidence score for the dialect detection (0.0 to 1.0)
  final double dialectConfidence;

  /// The confidence score for the accent detection (0.0 to 1.0)
  final double accentConfidence;

  /// Creates a new dialect accent info dialog
  const DialectAccentInfoDialog({
    Key? key,
    this.dialect,
    this.accent,
    this.dialectConfidence = 1.0,
    this.accentConfidence = 1.0,
  }) : super(key: key);

  @override
  ConsumerState<DialectAccentInfoDialog> createState() =>
      _DialectAccentInfoDialogState();
}

class _DialectAccentInfoDialogState
    extends ConsumerState<DialectAccentInfoDialog>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();

    // Initialize tab controller
    _tabController = TabController(
      length: widget.accent != null ? 3 : 2,
      vsync: this,
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Container(
        width: 0.9.sw,
        height: 0.7.sh,
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            _buildHeader(),

            SizedBox(height: 16.h),

            // Tab bar
            TabBar(
              controller: _tabController,
              labelColor: AppTheme.primaryColor,
              unselectedLabelColor: Colors.grey,
              indicatorColor: AppTheme.primaryColor,
              tabs: [
                Tab(text: 'Overview'),
                Tab(text: 'Details'),
                if (widget.accent != null) Tab(text: 'Accent'),
              ],
            ),

            SizedBox(height: 16.h),

            // Tab content
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildOverviewTab(),
                  _buildDetailsTab(),
                  if (widget.accent != null) _buildAccentTab(),
                ],
              ),
            ),

            // Actions
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                // Set as preferred button
                if (widget.dialect != null)
                  TextButton.icon(
                    onPressed: _setAsPreferred,
                    icon: const Icon(Icons.favorite_border),
                    label: const Text('Set as Preferred'),
                    style: TextButton.styleFrom(
                      foregroundColor: AppTheme.primaryColor,
                    ),
                  ),

                SizedBox(width: 8.w),

                // Close button
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Close'),
                  style: TextButton.styleFrom(
                    foregroundColor: Colors.grey,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Build the header
  Widget _buildHeader() {
    return Row(
      children: [
        // Flag
        if (widget.dialect != null) ...[
          Container(
            width: 40.r,
            height: 40.r,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.grey[200],
            ),
            child: Center(
              child: Text(
                widget.dialect!.flag,
                style: TextStyle(
                  fontSize: 24.r,
                ),
              ),
            ),
          ),
          SizedBox(width: 12.w),
        ],

        // Title
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Dialect name
              if (widget.dialect != null)
                Text(
                  widget.dialect!.name,
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),

              // Accent name
              if (widget.accent != null)
                Text(
                  widget.accent!.name,
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: AppTheme.textSecondaryColor,
                  ),
                ),

              // Region
              if (widget.dialect != null)
                Text(
                  widget.dialect!.region,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
            ],
          ),
        ),

        // Confidence indicator
        Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            // Dialect confidence
            if (widget.dialect != null)
              _buildConfidenceIndicator(
                'Dialect',
                widget.dialectConfidence,
              ),

            SizedBox(height: 4.h),

            // Accent confidence
            if (widget.accent != null)
              _buildConfidenceIndicator(
                'Accent',
                widget.accentConfidence,
              ),
          ],
        ),
      ],
    );
  }

  /// Build a confidence indicator
  Widget _buildConfidenceIndicator(String label, double confidence) {
    // Determine the color based on confidence
    Color color;
    String text;

    if (confidence >= 0.8) {
      color = Colors.green;
      text = 'High';
    } else if (confidence >= 0.6) {
      color = Colors.orange;
      text = 'Medium';
    } else {
      color = Colors.red;
      text = 'Low';
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          '$label: ',
          style: TextStyle(
            fontSize: 12.sp,
            color: AppTheme.textSecondaryColor,
          ),
        ),
        Container(
          padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
          decoration: BoxDecoration(
            color: color.withAlpha(26),
            borderRadius: BorderRadius.circular(4.r),
            border: Border.all(
              color: color.withAlpha(128),
              width: 1,
            ),
          ),
          child: Text(
            text,
            style: TextStyle(
              fontSize: 10.sp,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ),
      ],
    );
  }

  /// Build the overview tab
  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: EdgeInsets.symmetric(vertical: 8.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Dialect information
          if (widget.dialect != null) ...[
            _buildSectionHeader('About this Dialect'),

            SizedBox(height: 8.h),

            Text(
              'This dialect is primarily spoken in ${widget.dialect!.region} and has several distinctive features that set it apart from other dialects of the same language.',
              style: TextStyle(
                fontSize: 14.sp,
                color: AppTheme.textPrimaryColor,
              ),
            ),

            SizedBox(height: 16.h),

            // Dialect-specific terms
            _buildSectionHeader('Common Terms'),

            SizedBox(height: 8.h),

            _buildTermsGrid(widget.dialect!.dialectSpecificTerms),

            SizedBox(height: 16.h),
          ],

          // Accent information
          if (widget.accent != null) ...[
            _buildSectionHeader('About this Accent'),

            SizedBox(height: 8.h),

            Text(
              'This accent is characterized by its ${_getAccentDifficultyText(widget.accent!.difficulty)} pronunciation patterns and has several distinctive features.',
              style: TextStyle(
                fontSize: 14.sp,
                color: AppTheme.textPrimaryColor,
              ),
            ),

            SizedBox(height: 16.h),

            // Accent characteristics
            _buildSectionHeader('Key Characteristics'),

            SizedBox(height: 8.h),

            ...widget.accent!.characteristics.map((characteristic) {
              return _buildCharacteristicItem(characteristic);
            }).toList(),
          ],
        ],
      ),
    );
  }

  /// Build the details tab
  Widget _buildDetailsTab() {
    return SingleChildScrollView(
      padding: EdgeInsets.symmetric(vertical: 8.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Dialect details
          if (widget.dialect != null) ...[
            _buildSectionHeader('Dialect Details'),

            SizedBox(height: 8.h),

            _buildDetailItem('Code', widget.dialect!.code),
            _buildDetailItem('Language', widget.dialect!.languageCode),
            _buildDetailItem('Region', widget.dialect!.region),

            SizedBox(height: 16.h),

            // Offline availability
            _buildSectionHeader('Offline Availability'),

            SizedBox(height: 8.h),

            _buildOfflineAvailabilityInfo(
              widget.dialect!.isOfflineAvailable,
              widget.dialect!.isDownloaded,
              widget.dialect!.downloadSizeMB,
            ),

            SizedBox(height: 16.h),

            // Accent variants
            _buildSectionHeader('Accent Variants'),

            SizedBox(height: 8.h),

            widget.dialect!.accentVariants.isEmpty
                ? Text(
                    'No accent variants available for this dialect.',
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontStyle: FontStyle.italic,
                      color: AppTheme.textSecondaryColor,
                    ),
                  )
                : Column(
                    children: widget.dialect!.accentVariants.map((accentCode) {
                      return ListTile(
                        title: Text(_getAccentName(accentCode)),
                        leading: const Icon(Icons.record_voice_over),
                        dense: true,
                        contentPadding: EdgeInsets.zero,
                      );
                    }).toList(),
                  ),
          ],
        ],
      ),
    );
  }

  /// Build the accent tab
  Widget _buildAccentTab() {
    if (widget.accent == null) {
      return const Center(
        child: Text('No accent information available.'),
      );
    }

    return SingleChildScrollView(
      padding: EdgeInsets.symmetric(vertical: 8.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Accent details
          _buildSectionHeader('Accent Details'),

          SizedBox(height: 8.h),

          _buildDetailItem('Code', widget.accent!.code),
          _buildDetailItem('Dialect', widget.accent!.dialectCode),
          _buildDetailItem('Region', widget.accent!.region),
          _buildDetailItem('Difficulty',
              _getAccentDifficultyText(widget.accent!.difficulty)),

          SizedBox(height: 16.h),

          // Offline availability
          _buildSectionHeader('Offline Availability'),

          SizedBox(height: 8.h),

          _buildOfflineAvailabilityInfo(
            widget.accent!.isOfflineAvailable,
            widget.accent!.isDownloaded,
            widget.accent!.downloadSizeMB,
          ),

          SizedBox(height: 16.h),

          // Characteristics
          _buildSectionHeader('Characteristics'),

          SizedBox(height: 8.h),

          widget.accent!.characteristics.isEmpty
              ? Text(
                  'No characteristics available for this accent.',
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontStyle: FontStyle.italic,
                    color: AppTheme.textSecondaryColor,
                  ),
                )
              : Column(
                  children:
                      widget.accent!.characteristics.map((characteristic) {
                    return _buildCharacteristicItem(characteristic);
                  }).toList(),
                ),
        ],
      ),
    );
  }

  /// Build a section header
  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: TextStyle(
        fontSize: 16.sp,
        fontWeight: FontWeight.bold,
        color: AppTheme.primaryColor,
      ),
    );
  }

  /// Build a detail item
  Widget _buildDetailItem(String label, String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: 8.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100.w,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w500,
                color: AppTheme.textSecondaryColor,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14.sp,
                color: AppTheme.textPrimaryColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build a terms grid
  Widget _buildTermsGrid(List<String> terms) {
    return Wrap(
      spacing: 8.w,
      runSpacing: 8.h,
      children: terms.map((term) {
        return Container(
          padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
          decoration: BoxDecoration(
            color: AppTheme.primaryColor.withAlpha(26),
            borderRadius: BorderRadius.circular(4.r),
            border: Border.all(
              color: AppTheme.primaryColor.withAlpha(77),
              width: 1,
            ),
          ),
          child: Text(
            term,
            style: TextStyle(
              fontSize: 12.sp,
              color: AppTheme.primaryColor,
            ),
          ),
        );
      }).toList(),
    );
  }

  /// Build a characteristic item
  Widget _buildCharacteristicItem(AccentCharacteristic characteristic) {
    return Padding(
      padding: EdgeInsets.only(bottom: 12.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Type and description
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Icon(
                _getCharacteristicIcon(characteristic.type),
                size: 16.r,
                color: AppTheme.primaryColor,
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _getCharacteristicTypeText(characteristic.type),
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w500,
                        color: AppTheme.textPrimaryColor,
                      ),
                    ),
                    SizedBox(height: 2.h),
                    Text(
                      characteristic.description,
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: AppTheme.textSecondaryColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          // Examples
          if (characteristic.examples.isNotEmpty) ...[
            SizedBox(height: 4.h),
            Padding(
              padding: EdgeInsets.only(left: 24.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Examples:',
                    style: TextStyle(
                      fontSize: 12.sp,
                      fontWeight: FontWeight.w500,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                  SizedBox(height: 4.h),
                  Wrap(
                    spacing: 8.w,
                    runSpacing: 4.h,
                    children: characteristic.examples.map((example) {
                      return Container(
                        padding: EdgeInsets.symmetric(
                            horizontal: 6.w, vertical: 2.h),
                        decoration: BoxDecoration(
                          color: Colors.grey[200],
                          borderRadius: BorderRadius.circular(4.r),
                        ),
                        child: Text(
                          example,
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: AppTheme.textPrimaryColor,
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// Build offline availability info
  Widget _buildOfflineAvailabilityInfo(
    bool isOfflineAvailable,
    bool isDownloaded,
    double downloadSizeMB,
  ) {
    return Row(
      children: [
        Icon(
          isDownloaded
              ? Icons.check_circle
              : isOfflineAvailable
                  ? Icons.download
                  : Icons.cloud,
          size: 20.r,
          color: isDownloaded
              ? Colors.green
              : isOfflineAvailable
                  ? AppTheme.primaryColor
                  : Colors.grey,
        ),
        SizedBox(width: 8.w),
        Expanded(
          child: Text(
            isDownloaded
                ? 'Downloaded for offline use'
                : isOfflineAvailable
                    ? 'Available for offline use (${downloadSizeMB.toStringAsFixed(1)} MB)'
                    : 'Not available for offline use',
            style: TextStyle(
              fontSize: 14.sp,
              color: AppTheme.textPrimaryColor,
            ),
          ),
        ),
      ],
    );
  }

  /// Get the accent difficulty text
  String _getAccentDifficultyText(AccentDifficulty difficulty) {
    switch (difficulty) {
      case AccentDifficulty.easy:
        return 'Easy';
      case AccentDifficulty.moderate:
        return 'Moderate';
      case AccentDifficulty.difficult:
        return 'Difficult';
      case AccentDifficulty.veryDifficult:
        return 'Very Difficult';
    }
  }

  /// Get the characteristic type text
  String _getCharacteristicTypeText(AccentCharacteristicType type) {
    switch (type) {
      case AccentCharacteristicType.vowelSounds:
        return 'Vowel Sounds';
      case AccentCharacteristicType.consonantSounds:
        return 'Consonant Sounds';
      case AccentCharacteristicType.intonation:
        return 'Intonation';
      case AccentCharacteristicType.rhythm:
        return 'Rhythm';
      case AccentCharacteristicType.stress:
        return 'Stress';
      case AccentCharacteristicType.speed:
        return 'Speed';
    }
  }

  /// Get the characteristic icon
  IconData _getCharacteristicIcon(AccentCharacteristicType type) {
    switch (type) {
      case AccentCharacteristicType.vowelSounds:
        return Icons.record_voice_over;
      case AccentCharacteristicType.consonantSounds:
        return Icons.mic;
      case AccentCharacteristicType.intonation:
        return Icons.waves;
      case AccentCharacteristicType.rhythm:
        return Icons.music_note;
      case AccentCharacteristicType.stress:
        return Icons.format_bold;
      case AccentCharacteristicType.speed:
        return Icons.speed;
    }
  }

  /// Get a human-readable name for an accent code
  String _getAccentName(String accentCode) {
    // Parse the accent code (e.g., 'en-us-southern' -> 'Southern American')
    final parts = accentCode.split('-');
    if (parts.length < 3) {
      return accentCode;
    }

    final accentName = parts.sublist(2).join('-');

    // Capitalize the accent name
    final capitalizedAccentName = accentName.split('-').map((part) {
      return part.isNotEmpty ? part[0].toUpperCase() + part.substring(1) : '';
    }).join(' ');

    return capitalizedAccentName;
  }

  /// Set the dialect/accent as preferred
  void _setAsPreferred() {
    if (widget.dialect != null) {
      // Set the dialect as preferred for its language
      ref.read(dialectAccentDetectionServiceProvider).setPreferredDialect(
            widget.dialect!.languageCode,
            widget.dialect!.code,
          );

      // If there's an accent, set it as preferred for the dialect
      if (widget.accent != null) {
        ref.read(dialectAccentDetectionServiceProvider).setPreferredAccent(
              widget.dialect!.code,
              widget.accent!.code,
            );
      }

      // Show a snackbar
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            widget.accent != null
                ? 'Set ${widget.dialect!.name} with ${widget.accent!.name} accent as preferred'
                : 'Set ${widget.dialect!.name} as preferred dialect',
          ),
          duration: const Duration(seconds: 2),
        ),
      );

      // Close the dialog
      Navigator.of(context).pop();
    }
  }
}
