import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:culture_connect/models/translation/message_translation_metadata.dart';
import 'package:culture_connect/models/translation/translation_feedback_model.dart';
import 'package:culture_connect/providers/translation_feedback_provider.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// A widget for displaying translation feedback analytics
class TranslationFeedbackAnalytics extends ConsumerWidget {
  /// Creates a new translation feedback analytics widget
  const TranslationFeedbackAnalytics({super.key});
  
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final statisticsAsync = ref.watch(feedbackStatisticsProvider);
    
    return statisticsAsync.when(
      data: (statistics) => _buildContent(context, statistics),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stackTrace) => Center(
        child: Text(
          'Failed to load feedback statistics: $error',
          style: TextStyle(
            fontSize: 16.sp,
            color: Colors.red,
          ),
        ),
      ),
    );
  }
  
  /// Build the content
  Widget _buildContent(BuildContext context, Map<String, dynamic> statistics) {
    final totalFeedback = statistics['totalFeedback'] as int? ?? 0;
    
    if (totalFeedback == 0) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.feedback_outlined,
              size: 64.r,
              color: Colors.grey[300],
            ),
            SizedBox(height: 16.h),
            Text(
              'No feedback data available yet',
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              'Feedback data will appear here once users start providing feedback on translations',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14.sp,
                color: AppTheme.textSecondaryColor,
              ),
            ),
          ],
        ),
      );
    }
    
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.r),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Text(
            'Translation Feedback Analytics',
            style: TextStyle(
              fontSize: 20.sp,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          
          SizedBox(height: 8.h),
          
          Text(
            'Based on $totalFeedback feedback submissions',
            style: TextStyle(
              fontSize: 14.sp,
              color: AppTheme.textSecondaryColor,
            ),
          ),
          
          SizedBox(height: 24.h),
          
          // Quality distribution
          _buildQualityDistribution(statistics),
          
          SizedBox(height: 24.h),
          
          // Feedback type distribution
          _buildFeedbackTypeDistribution(statistics),
          
          SizedBox(height: 24.h),
          
          // Language pairs
          _buildLanguagePairs(statistics),
        ],
      ),
    );
  }
  
  /// Build the quality distribution section
  Widget _buildQualityDistribution(Map<String, dynamic> statistics) {
    final qualityDistribution = statistics['qualityDistribution'] as Map<String, dynamic>? ?? {};
    final totalFeedback = statistics['totalFeedback'] as int? ?? 0;
    
    if (qualityDistribution.isEmpty) {
      return const SizedBox.shrink();
    }
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quality Distribution',
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.bold,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        
        SizedBox(height: 16.h),
        
        ...TranslationQuality.values.map((quality) {
          final count = qualityDistribution[quality.index.toString()] as int? ?? 0;
          final percentage = totalFeedback > 0 ? count / totalFeedback : 0.0;
          
          return _buildDistributionItem(
            icon: quality.icon,
            color: quality.color,
            label: quality.displayName,
            count: count,
            percentage: percentage,
          );
        }).toList(),
      ],
    );
  }
  
  /// Build the feedback type distribution section
  Widget _buildFeedbackTypeDistribution(Map<String, dynamic> statistics) {
    final typeDistribution = statistics['typeDistribution'] as Map<String, dynamic>? ?? {};
    final totalFeedback = statistics['totalFeedback'] as int? ?? 0;
    
    if (typeDistribution.isEmpty) {
      return const SizedBox.shrink();
    }
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Feedback Type Distribution',
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.bold,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        
        SizedBox(height: 16.h),
        
        ...TranslationFeedbackType.values.map((type) {
          final count = typeDistribution[type.index.toString()] as int? ?? 0;
          final percentage = totalFeedback > 0 ? count / totalFeedback : 0.0;
          
          return _buildDistributionItem(
            icon: type.icon,
            color: AppTheme.primaryColor,
            label: type.displayName,
            count: count,
            percentage: percentage,
          );
        }).toList(),
      ],
    );
  }
  
  /// Build the language pairs section
  Widget _buildLanguagePairs(Map<String, dynamic> statistics) {
    final languagePairs = statistics['languagePairs'] as Map<String, dynamic>? ?? {};
    final totalFeedback = statistics['totalFeedback'] as int? ?? 0;
    
    if (languagePairs.isEmpty) {
      return const SizedBox.shrink();
    }
    
    // Sort language pairs by count
    final sortedPairs = languagePairs.entries.toList()
      ..sort((a, b) => (b.value as int).compareTo(a.value as int));
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Language Pairs',
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.bold,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        
        SizedBox(height: 16.h),
        
        ...sortedPairs.take(5).map((entry) {
          final pair = entry.key as String;
          final count = entry.value as int;
          final percentage = totalFeedback > 0 ? count / totalFeedback : 0.0;
          
          final languages = pair.split('-');
          final sourceLanguage = _getLanguageName(languages[0]);
          final targetLanguage = _getLanguageName(languages[1]);
          
          return _buildDistributionItem(
            icon: Icons.language,
            color: Colors.blue,
            label: '$sourceLanguage → $targetLanguage',
            count: count,
            percentage: percentage,
          );
        }).toList(),
      ],
    );
  }
  
  /// Build a distribution item
  Widget _buildDistributionItem({
    required IconData icon,
    required Color color,
    required String label,
    required int count,
    required double percentage,
  }) {
    return Padding(
      padding: EdgeInsets.only(bottom: 12.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                size: 16.r,
                color: color,
              ),
              SizedBox(width: 8.w),
              Text(
                label,
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
              const Spacer(),
              Text(
                '$count (${(percentage * 100).toStringAsFixed(1)}%)',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: AppTheme.textSecondaryColor,
                ),
              ),
            ],
          ),
          
          SizedBox(height: 4.h),
          
          // Progress bar
          Container(
            height: 8.h,
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(4.r),
            ),
            child: FractionallySizedBox(
              alignment: Alignment.centerLeft,
              widthFactor: percentage,
              child: Container(
                decoration: BoxDecoration(
                  color: color,
                  borderRadius: BorderRadius.circular(4.r),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  /// Get the language name from a language code
  String _getLanguageName(String code) {
    switch (code) {
      case 'en':
        return 'English';
      case 'fr':
        return 'French';
      case 'yo':
        return 'Yoruba';
      case 'ig':
        return 'Igbo';
      case 'ha':
        return 'Hausa';
      case 'sw':
        return 'Swahili';
      default:
        return code;
    }
  }
}
