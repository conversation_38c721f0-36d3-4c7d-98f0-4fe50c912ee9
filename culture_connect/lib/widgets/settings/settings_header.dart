import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// A header widget for settings screens
class SettingsHeader extends StatelessWidget {
  /// The title of the header
  final String title;
  
  /// Optional subtitle text
  final String? subtitle;
  
  /// Optional icon to display
  final IconData? icon;
  
  /// Optional action widget to display on the right
  final Widget? action;
  
  /// Optional background color
  final Color? backgroundColor;
  
  /// Optional text color
  final Color? textColor;
  
  /// Optional icon color
  final Color? iconColor;
  
  /// Optional padding
  final EdgeInsetsGeometry? padding;
  
  /// Creates a settings header widget
  const SettingsHeader({
    super.key,
    required this.title,
    this.subtitle,
    this.icon,
    this.action,
    this.backgroundColor,
    this.textColor,
    this.iconColor,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: padding ?? EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      color: backgroundColor ?? theme.colorScheme.surface,
      child: Row(
        children: [
          if (icon != null) ...[
            Icon(
              icon,
              color: iconColor ?? theme.colorScheme.primary,
              size: 24.sp,
            ),
            SizedBox(width: 12.w),
          ],
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: theme.textTheme.titleMedium?.copyWith(
                    color: textColor ?? theme.colorScheme.onSurface,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (subtitle != null) ...[
                  SizedBox(height: 4.h),
                  Text(
                    subtitle!,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: textColor?.withOpacity(0.7) ?? 
                             theme.colorScheme.onSurface.withOpacity(0.7),
                    ),
                  ),
                ],
              ],
            ),
          ),
          if (action != null) action!,
        ],
      ),
    );
  }
}
