import 'dart:math';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:culture_connect/models/travel/price_alert.dart';
import 'package:culture_connect/models/travel/travel_service_base.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/services/notification_service.dart';

/// Service for managing price alerts
class PriceAlertService {
  final FirebaseFirestore? _firestore;
  final LoggingService _loggingService;
  final NotificationService _notificationService;
  final String _userId;

  /// Whether the service has been initialized
  bool _isInitialized = false;

  /// Mock data for price alerts
  List<PriceAlert> _priceAlerts = [];

  /// Creates a new price alert service
  PriceAlertService({
    FirebaseFirestore? firestore,
    required LoggingService loggingService,
    required NotificationService notificationService,
    required String userId,
  })  : _firestore = firestore,
        _loggingService = loggingService,
        _notificationService = notificationService,
        _userId = userId;

  /// Initialize the service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _loggingService.debug(
        'PriceAlertService',
        'Initializing price alert service',
      );

      // Try to get data from Firestore
      if (_firestore != null) {
        try {
          final alertsSnapshot = await _firestore!
              .collection('price_alerts')
              .where('userId', isEqualTo: _userId)
              .where('status', whereIn: [
            'active',
            'triggered',
          ]).get();

          if (alertsSnapshot.docs.isNotEmpty) {
            _priceAlerts = alertsSnapshot.docs
                .map((doc) => _priceAlertFromFirestore(doc))
                .toList();
          } else {
            // Fallback to mock data
            _generateMockData();
          }
        } catch (e) {
          _loggingService.error(
            'PriceAlertService',
            'Error getting data from Firestore',
            e,
            StackTrace.current,
          );
          // Fallback to mock data
          _generateMockData();
        }
      } else {
        // No Firestore, use mock data
        _generateMockData();
      }

      _isInitialized = true;

      _loggingService.debug(
        'PriceAlertService',
        'Price alert service initialized with ${_priceAlerts.length} alerts',
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'PriceAlertService',
        'Error initializing price alert service',
        e,
        stackTrace,
      );
      rethrow;
    }
  }

  /// Get all price alerts for the current user
  Future<List<PriceAlert>> getAllPriceAlerts() async {
    await initialize();
    return _priceAlerts;
  }

  /// Get active price alerts for the current user
  Future<List<PriceAlert>> getActivePriceAlerts() async {
    await initialize();
    return _priceAlerts
        .where((alert) => alert.status == PriceAlertStatus.active)
        .toList();
  }

  /// Get triggered price alerts for the current user
  Future<List<PriceAlert>> getTriggeredPriceAlerts() async {
    await initialize();
    return _priceAlerts
        .where((alert) => alert.status == PriceAlertStatus.triggered)
        .toList();
  }

  /// Get price alerts for a specific travel service
  Future<List<PriceAlert>> getPriceAlertsForService(
      String travelServiceId) async {
    await initialize();
    return _priceAlerts
        .where((alert) => alert.travelServiceId == travelServiceId)
        .toList();
  }

  /// Get a price alert by ID
  Future<PriceAlert?> getPriceAlertById(String alertId) async {
    await initialize();
    try {
      return _priceAlerts.firstWhere((alert) => alert.id == alertId);
    } catch (e) {
      return null;
    }
  }

  /// Create a new price alert
  Future<PriceAlert> createPriceAlert({
    required String travelServiceId,
    required TravelServiceType travelServiceType,
    required String travelServiceName,
    required double currentPrice,
    required double targetPrice,
    required String currency,
    required AlertFrequency frequency,
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    await initialize();

    try {
      _loggingService.debug(
        'PriceAlertService',
        'Creating price alert for $travelServiceName',
      );

      final now = DateTime.now();

      // Create the price alert
      final priceAlert = PriceAlert(
        id: 'alert-${now.millisecondsSinceEpoch}',
        userId: _userId,
        travelServiceId: travelServiceId,
        travelServiceType: travelServiceType,
        travelServiceName: travelServiceName,
        currentPrice: currentPrice,
        targetPrice: targetPrice,
        currency: currency,
        frequency: frequency,
        status: PriceAlertStatus.active,
        startDate: startDate,
        endDate: endDate,
        createdAt: now,
        updatedAt: now,
        priceHistory: [
          PriceHistoryPoint(
            date: now,
            price: currentPrice,
          ),
        ],
      );

      // Save to Firestore if available
      if (_firestore != null) {
        try {
          await _firestore!
              .collection('price_alerts')
              .doc(priceAlert.id)
              .set(_priceAlertToFirestore(priceAlert));
        } catch (e) {
          _loggingService.error(
            'PriceAlertService',
            'Error saving price alert to Firestore',
            e,
            StackTrace.current,
          );
        }
      }

      // Add to local cache
      _priceAlerts.add(priceAlert);

      // Send confirmation notification
      _notificationService.showNotification(
        title: 'Price Alert Created',
        body:
            'We\'ll notify you when $travelServiceName drops below ${priceAlert.formattedTargetPrice}',
        payload: {
          'type': 'price_alert',
          'action': 'created',
          'alertId': priceAlert.id,
        },
      );

      return priceAlert;
    } catch (e, stackTrace) {
      _loggingService.error(
        'PriceAlertService',
        'Error creating price alert',
        e,
        stackTrace,
      );
      rethrow;
    }
  }

  /// Update a price alert
  Future<PriceAlert> updatePriceAlert({
    required String alertId,
    double? targetPrice,
    AlertFrequency? frequency,
    DateTime? endDate,
  }) async {
    await initialize();

    try {
      _loggingService.debug(
        'PriceAlertService',
        'Updating price alert $alertId',
      );

      // Find the alert
      final alertIndex =
          _priceAlerts.indexWhere((alert) => alert.id == alertId);
      if (alertIndex == -1) {
        throw Exception('Price alert not found');
      }

      final alert = _priceAlerts[alertIndex];

      // Update the alert
      final updatedAlert = alert.copyWith(
        targetPrice: targetPrice,
        frequency: frequency,
        endDate: endDate,
        updatedAt: DateTime.now(),
      );

      // Save to Firestore if available
      if (_firestore != null) {
        try {
          await _firestore!
              .collection('price_alerts')
              .doc(alertId)
              .update(_priceAlertToFirestore(updatedAlert));
        } catch (e) {
          _loggingService.error(
            'PriceAlertService',
            'Error updating price alert in Firestore',
            e,
            StackTrace.current,
          );
        }
      }

      // Update local cache
      _priceAlerts[alertIndex] = updatedAlert;

      return updatedAlert;
    } catch (e, stackTrace) {
      _loggingService.error(
        'PriceAlertService',
        'Error updating price alert',
        e,
        stackTrace,
      );
      rethrow;
    }
  }

  /// Delete a price alert
  Future<void> deletePriceAlert(String alertId) async {
    await initialize();

    try {
      _loggingService.debug(
        'PriceAlertService',
        'Deleting price alert $alertId',
      );

      // Find the alert
      final alertIndex =
          _priceAlerts.indexWhere((alert) => alert.id == alertId);
      if (alertIndex == -1) {
        throw Exception('Price alert not found');
      }

      final alert = _priceAlerts[alertIndex];

      // Update the alert status
      final updatedAlert = alert.copyWith(
        status: PriceAlertStatus.deleted,
        updatedAt: DateTime.now(),
      );

      // Save to Firestore if available
      if (_firestore != null) {
        try {
          await _firestore!.collection('price_alerts').doc(alertId).update({
            'status': 'deleted',
            'updatedAt': FieldValue.serverTimestamp(),
          });
        } catch (e) {
          _loggingService.error(
            'PriceAlertService',
            'Error deleting price alert in Firestore',
            e,
            StackTrace.current,
          );
        }
      }

      // Update local cache
      _priceAlerts[alertIndex] = updatedAlert;
    } catch (e, stackTrace) {
      _loggingService.error(
        'PriceAlertService',
        'Error deleting price alert',
        e,
        stackTrace,
      );
      rethrow;
    }
  }

  /// Check price alerts for updates
  Future<void> checkPriceAlerts() async {
    await initialize();

    try {
      _loggingService.debug(
        'PriceAlertService',
        'Checking price alerts',
      );

      final now = DateTime.now();
      final random = Random();

      // Get active alerts
      final activeAlerts = await getActivePriceAlerts();

      for (final alert in activeAlerts) {
        // Skip expired alerts
        if (alert.endDate.isBefore(now)) {
          await _markAlertAsExpired(alert.id);
          continue;
        }

        // In a real app, this would fetch the current price from an API
        // For now, simulate price changes
        final priceChange = random.nextDouble() * 50 - 25; // -25 to +25
        final newPrice = max(alert.currentPrice + priceChange, 1.0);

        // Add to price history
        final updatedHistory = List<PriceHistoryPoint>.from(alert.priceHistory)
          ..add(PriceHistoryPoint(
            date: now,
            price: newPrice,
          ));

        // Check if price dropped below target
        if (newPrice <= alert.targetPrice) {
          // Price alert triggered!
          await _triggerPriceAlert(
            alertId: alert.id,
            newPrice: newPrice,
            priceHistory: updatedHistory,
          );
        } else {
          // Just update the price
          await _updatePriceHistory(
            alertId: alert.id,
            newPrice: newPrice,
            priceHistory: updatedHistory,
          );
        }
      }
    } catch (e, stackTrace) {
      _loggingService.error(
        'PriceAlertService',
        'Error checking price alerts',
        e,
        stackTrace,
      );
    }
  }

  /// Mark an alert as expired
  Future<void> _markAlertAsExpired(String alertId) async {
    try {
      // Find the alert
      final alertIndex =
          _priceAlerts.indexWhere((alert) => alert.id == alertId);
      if (alertIndex == -1) {
        return;
      }

      final alert = _priceAlerts[alertIndex];

      // Update the alert status
      final updatedAlert = alert.copyWith(
        status: PriceAlertStatus.expired,
        updatedAt: DateTime.now(),
      );

      // Save to Firestore if available
      if (_firestore != null) {
        try {
          await _firestore!.collection('price_alerts').doc(alertId).update({
            'status': 'expired',
            'updatedAt': FieldValue.serverTimestamp(),
          });
        } catch (e) {
          _loggingService.error(
            'PriceAlertService',
            'Error marking alert as expired in Firestore',
            e,
            StackTrace.current,
          );
        }
      }

      // Update local cache
      _priceAlerts[alertIndex] = updatedAlert;

      // Send notification
      _notificationService.showNotification(
        title: 'Price Alert Expired',
        body: 'Your price alert for ${alert.travelServiceName} has expired',
        payload: {
          'type': 'price_alert',
          'action': 'expired',
          'alertId': alert.id,
        },
      );
    } catch (e) {
      _loggingService.error(
        'PriceAlertService',
        'Error marking alert as expired',
        e,
        StackTrace.current,
      );
    }
  }

  /// Trigger a price alert
  Future<void> _triggerPriceAlert({
    required String alertId,
    required double newPrice,
    required List<PriceHistoryPoint> priceHistory,
  }) async {
    try {
      // Find the alert
      final alertIndex =
          _priceAlerts.indexWhere((alert) => alert.id == alertId);
      if (alertIndex == -1) {
        return;
      }

      final alert = _priceAlerts[alertIndex];

      // Update the alert
      final updatedAlert = alert.copyWith(
        status: PriceAlertStatus.triggered,
        currentPrice: newPrice,
        lastPrice: alert.currentPrice,
        lastCheckedAt: DateTime.now(),
        updatedAt: DateTime.now(),
        priceHistory: priceHistory,
      );

      // Save to Firestore if available
      if (_firestore != null) {
        try {
          await _firestore!
              .collection('price_alerts')
              .doc(alertId)
              .update(_priceAlertToFirestore(updatedAlert));
        } catch (e) {
          _loggingService.error(
            'PriceAlertService',
            'Error triggering price alert in Firestore',
            e,
            StackTrace.current,
          );
        }
      }

      // Update local cache
      _priceAlerts[alertIndex] = updatedAlert;

      // Send notification
      final priceDrop = alert.currentPrice - newPrice;
      final priceDropPercentage = (priceDrop / alert.currentPrice) * 100;

      _notificationService.showNotification(
        title: 'Price Drop Alert! 🎉',
        body:
            '${alert.travelServiceName} price dropped by ${alert.currency}${priceDrop.toStringAsFixed(2)} (${priceDropPercentage.toStringAsFixed(1)}%)! Now ${alert.currency}${newPrice.toStringAsFixed(2)}',
        payload: {
          'type': 'price_alert',
          'action': 'triggered',
          'alertId': alert.id,
        },
      );
    } catch (e) {
      _loggingService.error(
        'PriceAlertService',
        'Error triggering price alert',
        e,
        StackTrace.current,
      );
    }
  }

  /// Update price history
  Future<void> _updatePriceHistory({
    required String alertId,
    required double newPrice,
    required List<PriceHistoryPoint> priceHistory,
  }) async {
    try {
      // Find the alert
      final alertIndex =
          _priceAlerts.indexWhere((alert) => alert.id == alertId);
      if (alertIndex == -1) {
        return;
      }

      final alert = _priceAlerts[alertIndex];

      // Update the alert
      final updatedAlert = alert.copyWith(
        currentPrice: newPrice,
        lastPrice: alert.currentPrice,
        lastCheckedAt: DateTime.now(),
        updatedAt: DateTime.now(),
        priceHistory: priceHistory,
      );

      // Save to Firestore if available
      if (_firestore != null) {
        try {
          await _firestore!.collection('price_alerts').doc(alertId).update({
            'currentPrice': newPrice,
            'lastPrice': alert.currentPrice,
            'lastCheckedAt': FieldValue.serverTimestamp(),
            'updatedAt': FieldValue.serverTimestamp(),
            'priceHistory': priceHistory
                .map((point) => {
                      'date': Timestamp.fromDate(point.date),
                      'price': point.price,
                    })
                .toList(),
          });
        } catch (e) {
          _loggingService.error(
            'PriceAlertService',
            'Error updating price history in Firestore',
            e,
            StackTrace.current,
          );
        }
      }

      // Update local cache
      _priceAlerts[alertIndex] = updatedAlert;
    } catch (e) {
      _loggingService.error(
        'PriceAlertService',
        'Error updating price history',
        e,
        StackTrace.current,
      );
    }
  }

  /// Generate mock data
  void _generateMockData() {
    final random = Random();
    final now = DateTime.now();

    // Generate mock price alerts
    _priceAlerts = [
      PriceAlert(
        id: 'alert-1',
        userId: _userId,
        travelServiceId: 'flight-JFK-LAX-15-0',
        travelServiceType: TravelServiceType.flight,
        travelServiceName: 'Flight from New York to Los Angeles',
        currentPrice: 350.0,
        targetPrice: 300.0,
        currency: 'USD',
        frequency: AlertFrequency.immediate,
        status: PriceAlertStatus.active,
        startDate: now,
        endDate: now.add(const Duration(days: 30)),
        createdAt: now.subtract(const Duration(days: 2)),
        updatedAt: now.subtract(const Duration(days: 2)),
        lastCheckedAt: now.subtract(const Duration(hours: 6)),
        lastPrice: 355.0,
        priceHistory: [
          PriceHistoryPoint(
            date: now.subtract(const Duration(days: 2)),
            price: 360.0,
          ),
          PriceHistoryPoint(
            date: now.subtract(const Duration(days: 1)),
            price: 355.0,
          ),
          PriceHistoryPoint(
            date: now,
            price: 350.0,
          ),
        ],
      ),
      PriceAlert(
        id: 'alert-2',
        userId: _userId,
        travelServiceId: 'hotel-1',
        travelServiceType: TravelServiceType.hotel,
        travelServiceName: 'Grand Hotel New York',
        currentPrice: 250.0,
        targetPrice: 200.0,
        currency: 'USD',
        frequency: AlertFrequency.daily,
        status: PriceAlertStatus.active,
        startDate: now,
        endDate: now.add(const Duration(days: 60)),
        createdAt: now.subtract(const Duration(days: 5)),
        updatedAt: now.subtract(const Duration(days: 5)),
        lastCheckedAt: now.subtract(const Duration(hours: 12)),
        lastPrice: 260.0,
        priceHistory: [
          PriceHistoryPoint(
            date: now.subtract(const Duration(days: 5)),
            price: 270.0,
          ),
          PriceHistoryPoint(
            date: now.subtract(const Duration(days: 3)),
            price: 260.0,
          ),
          PriceHistoryPoint(
            date: now.subtract(const Duration(days: 1)),
            price: 250.0,
          ),
        ],
      ),
      PriceAlert(
        id: 'alert-3',
        userId: _userId,
        travelServiceId: 'cruise-1',
        travelServiceType: TravelServiceType.cruise,
        travelServiceName: 'Caribbean Cruise',
        currentPrice: 800.0,
        targetPrice: 700.0,
        currency: 'USD',
        frequency: AlertFrequency.weekly,
        status: PriceAlertStatus.triggered,
        startDate: now.subtract(const Duration(days: 10)),
        endDate: now.add(const Duration(days: 20)),
        createdAt: now.subtract(const Duration(days: 10)),
        updatedAt: now.subtract(const Duration(days: 1)),
        lastCheckedAt: now.subtract(const Duration(days: 1)),
        lastPrice: 850.0,
        priceHistory: [
          PriceHistoryPoint(
            date: now.subtract(const Duration(days: 10)),
            price: 900.0,
          ),
          PriceHistoryPoint(
            date: now.subtract(const Duration(days: 7)),
            price: 850.0,
          ),
          PriceHistoryPoint(
            date: now.subtract(const Duration(days: 4)),
            price: 820.0,
          ),
          PriceHistoryPoint(
            date: now.subtract(const Duration(days: 1)),
            price: 700.0,
          ),
        ],
      ),
    ];
  }

  /// Convert a Firestore document to a PriceAlert
  PriceAlert _priceAlertFromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    return PriceAlert(
      id: doc.id,
      userId: data['userId'] as String,
      travelServiceId: data['travelServiceId'] as String,
      travelServiceType: TravelServiceType.values.firstWhere(
        (type) => type.toString() == data['travelServiceType'] as String,
        orElse: () => TravelServiceType.flight,
      ),
      travelServiceName: data['travelServiceName'] as String,
      currentPrice: (data['currentPrice'] as num).toDouble(),
      targetPrice: (data['targetPrice'] as num).toDouble(),
      currency: data['currency'] as String,
      frequency: AlertFrequency.values.firstWhere(
        (freq) => freq.toString() == data['frequency'] as String,
        orElse: () => AlertFrequency.immediate,
      ),
      status: PriceAlertStatus.values.firstWhere(
        (status) => status.toString() == data['status'] as String,
        orElse: () => PriceAlertStatus.active,
      ),
      startDate: (data['startDate'] as Timestamp).toDate(),
      endDate: (data['endDate'] as Timestamp).toDate(),
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: (data['updatedAt'] as Timestamp).toDate(),
      lastCheckedAt: data['lastCheckedAt'] != null
          ? (data['lastCheckedAt'] as Timestamp).toDate()
          : null,
      lastPrice: data['lastPrice'] != null
          ? (data['lastPrice'] as num).toDouble()
          : null,
      priceHistory: data['priceHistory'] != null
          ? (data['priceHistory'] as List)
              .map((point) => PriceHistoryPoint(
                    date: (point['date'] as Timestamp).toDate(),
                    price: (point['price'] as num).toDouble(),
                  ))
              .toList()
          : [],
    );
  }

  /// Convert a PriceAlert to a Firestore document
  Map<String, dynamic> _priceAlertToFirestore(PriceAlert alert) {
    return {
      'userId': alert.userId,
      'travelServiceId': alert.travelServiceId,
      'travelServiceType': alert.travelServiceType.toString(),
      'travelServiceName': alert.travelServiceName,
      'currentPrice': alert.currentPrice,
      'targetPrice': alert.targetPrice,
      'currency': alert.currency,
      'frequency': alert.frequency.toString(),
      'status': alert.status.toString(),
      'startDate': Timestamp.fromDate(alert.startDate),
      'endDate': Timestamp.fromDate(alert.endDate),
      'createdAt': Timestamp.fromDate(alert.createdAt),
      'updatedAt': Timestamp.fromDate(alert.updatedAt),
      'lastCheckedAt': alert.lastCheckedAt != null
          ? Timestamp.fromDate(alert.lastCheckedAt!)
          : null,
      'lastPrice': alert.lastPrice,
      'priceHistory': alert.priceHistory
          .map((point) => {
                'date': Timestamp.fromDate(point.date),
                'price': point.price,
              })
          .toList(),
    };
  }
}
