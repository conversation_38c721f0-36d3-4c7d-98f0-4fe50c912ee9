import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:culture_connect/models/safety_model.dart';
import 'package:culture_connect/providers/auth_provider.dart';
import 'package:culture_connect/services/location_service.dart';

class SafetyService {
  final FirebaseFirestore _firestore;
  final String _userId;
  final LocationService _locationService;

  SafetyService(this._firestore, this._userId, this._locationService);

  // Emergency Contacts
  Future<List<EmergencyContact>> getEmergencyContacts() async {
    try {
      final snapshot = await _firestore
          .collection('emergency_contacts')
          .where('userId', isEqualTo: _userId)
          .orderBy('isPrimary', descending: true)
          .orderBy('name')
          .get();

      return snapshot.docs
          .map(
              (doc) => EmergencyContact.fromJson({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      rethrow;
    }
  }

  Future<EmergencyContact> addEmergencyContact(EmergencyContact contact) async {
    try {
      // If this is a primary contact, update all other contacts to non-primary
      if (contact.isPrimary) {
        await _updatePrimaryContactStatus();
      }

      final docRef = _firestore.collection('emergency_contacts').doc();
      final now = DateTime.now();

      final newContact = EmergencyContact(
        id: docRef.id,
        userId: _userId,
        name: contact.name,
        phoneNumber: contact.phoneNumber,
        email: contact.email,
        relationship: contact.relationship,
        type: contact.type,
        notes: contact.notes,
        isPrimary: contact.isPrimary,
        createdAt: now,
        updatedAt: now,
      );

      await docRef.set(newContact.toJson());
      return newContact;
    } catch (e) {
      rethrow;
    }
  }

  Future<void> updateEmergencyContact(EmergencyContact contact) async {
    try {
      // If this is a primary contact, update all other contacts to non-primary
      if (contact.isPrimary) {
        await _updatePrimaryContactStatus();
      }

      final updatedContact = contact.copyWith(
        updatedAt: DateTime.now(),
      );

      await _firestore
          .collection('emergency_contacts')
          .doc(contact.id)
          .update(updatedContact.toJson());
    } catch (e) {
      rethrow;
    }
  }

  Future<void> deleteEmergencyContact(String contactId) async {
    try {
      await _firestore.collection('emergency_contacts').doc(contactId).delete();
    } catch (e) {
      rethrow;
    }
  }

  Future<void> _updatePrimaryContactStatus() async {
    try {
      final snapshot = await _firestore
          .collection('emergency_contacts')
          .where('userId', isEqualTo: _userId)
          .where('isPrimary', isEqualTo: true)
          .get();

      final batch = _firestore.batch();
      for (final doc in snapshot.docs) {
        batch.update(doc.reference, {'isPrimary': false});
      }
      await batch.commit();
    } catch (e) {
      rethrow;
    }
  }

  // Safety Alerts
  Future<SafetyAlert> createSosAlert({String? message}) async {
    try {
      final position = await _locationService.getCurrentPosition();
      final location = LatLng(position.latitude, position.longitude);

      final docRef = _firestore.collection('safety_alerts').doc();
      final now = DateTime.now();

      final alert = SafetyAlert(
        id: docRef.id,
        userId: _userId,
        type: SafetyAlertType.sos,
        status: SafetyAlertStatus.active,
        createdAt: now,
        location: location,
        message: message,
      );

      await docRef.set(alert.toJson());

      // Notify emergency contacts
      await _notifyEmergencyContacts(alert);

      return alert;
    } catch (e) {
      rethrow;
    }
  }

  Future<SafetyAlert> createCheckInAlert() async {
    try {
      final position = await _locationService.getCurrentPosition();
      final location = LatLng(position.latitude, position.longitude);

      final docRef = _firestore.collection('safety_alerts').doc();
      final now = DateTime.now();

      final alert = SafetyAlert(
        id: docRef.id,
        userId: _userId,
        type: SafetyAlertType.checkIn,
        status: SafetyAlertStatus.active,
        createdAt: now,
        location: location,
      );

      await docRef.set(alert.toJson());
      return alert;
    } catch (e) {
      rethrow;
    }
  }

  Future<SafetyAlert> shareLocation(List<String> recipientIds,
      {String? message}) async {
    try {
      final position = await _locationService.getCurrentPosition();
      final location = LatLng(position.latitude, position.longitude);

      final docRef = _firestore.collection('safety_alerts').doc();
      final now = DateTime.now();

      final alert = SafetyAlert(
        id: docRef.id,
        userId: _userId,
        type: SafetyAlertType.locationShare,
        status: SafetyAlertStatus.active,
        createdAt: now,
        location: location,
        message: message,
        recipientIds: recipientIds,
      );

      await docRef.set(alert.toJson());
      return alert;
    } catch (e) {
      rethrow;
    }
  }

  Future<void> resolveAlert(String alertId) async {
    try {
      await _firestore.collection('safety_alerts').doc(alertId).update({
        'status': SafetyAlertStatus.resolved.toString().split('.').last,
        'resolvedAt': DateTime.now(),
      });
    } catch (e) {
      rethrow;
    }
  }

  Future<void> cancelAlert(String alertId) async {
    try {
      await _firestore.collection('safety_alerts').doc(alertId).update({
        'status': SafetyAlertStatus.cancelled.toString().split('.').last,
        'resolvedAt': DateTime.now(),
      });
    } catch (e) {
      rethrow;
    }
  }

  Future<List<SafetyAlert>> getUserAlerts() async {
    try {
      final snapshot = await _firestore
          .collection('safety_alerts')
          .where('userId', isEqualTo: _userId)
          .orderBy('createdAt', descending: true)
          .get();

      return snapshot.docs
          .map((doc) => SafetyAlert.fromJson({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      rethrow;
    }
  }

  // Safe Zones
  Future<List<SafeZone>> getSafeZones(
      LatLng location, double radiusInKm) async {
    try {
      // For demo purposes, we'll return a list of hardcoded safe zones
      // In a real app, you would query Firestore with GeoFirestore
      return [
        SafeZone(
          id: '1',
          name: 'Police Station',
          description: 'Local police station with 24/7 service',
          location: LatLng(location.latitude + 0.01, location.longitude + 0.01),
          radius: 100,
          address: '123 Safety St, Lagos',
          phoneNumber: '+2341234567890',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        SafeZone(
          id: '2',
          name: 'Hospital',
          description: 'General hospital with emergency services',
          location: LatLng(location.latitude - 0.01, location.longitude - 0.01),
          radius: 150,
          address: '456 Health Ave, Lagos',
          phoneNumber: '+2349876543210',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        SafeZone(
          id: '3',
          name: 'Embassy',
          description: 'US Embassy',
          location: LatLng(location.latitude + 0.02, location.longitude - 0.02),
          radius: 200,
          address: '789 Diplomatic Blvd, Lagos',
          phoneNumber: '+2345678901234',
          website: 'https://ng.usembassy.gov',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      ];
    } catch (e) {
      rethrow;
    }
  }

  // Safety Tips
  Future<List<SafetyTip>> getSafetyTips(
      {String? category, String? countryCode}) async {
    try {
      // For demo purposes, we'll return a list of hardcoded safety tips
      // In a real app, you would query Firestore
      return [
        SafetyTip(
          id: '1',
          title: 'Stay aware of your surroundings',
          content:
              'Always be mindful of your environment and the people around you, especially in crowded areas.',
          category: 'general',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        SafetyTip(
          id: '2',
          title: 'Keep emergency contacts handy',
          content:
              'Save local emergency numbers and your embassy contact information in your phone.',
          category: 'contacts',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        SafetyTip(
          id: '3',
          title: 'Share your itinerary',
          content:
              'Let someone you trust know your travel plans, including accommodations and scheduled activities.',
          category: 'travel',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      ];
    } catch (e) {
      rethrow;
    }
  }

  // Emergency Calling
  Future<void> callEmergency(String phoneNumber) async {
    final Uri uri = Uri(scheme: 'tel', path: phoneNumber);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    } else {
      throw 'Could not launch $uri';
    }
  }

  // Helper methods
  Future<void> _notifyEmergencyContacts(SafetyAlert alert) async {
    try {
      // Get primary emergency contact
      final contacts = await getEmergencyContacts();
      final primaryContact = contacts.firstWhere(
        (contact) => contact.isPrimary,
        orElse: () =>
            contacts.isNotEmpty ? contacts.first : null as EmergencyContact,
      );

      // In a real app, you would send SMS or push notifications
      // For demo purposes, we'll just log it
      print('Notifying ${primaryContact.name} about SOS alert');
    } catch (e) {
      // Log error but don't rethrow - we don't want to prevent the SOS alert from being created
      print('Error notifying emergency contacts: $e');
    }
  }
}

final safetyServiceProvider = Provider<SafetyService>((ref) {
  final user = ref.watch(authStateProvider).user;
  if (user == null) {
    throw Exception('User must be logged in to access safety services');
  }

  final firestore = FirebaseFirestore.instance;
  final locationService = ref.watch(locationServiceProvider);

  return SafetyService(firestore, user.id, locationService);
});

final emergencyContactsProvider = StreamProvider<List<EmergencyContact>>((ref) {
  final user = ref.watch(authStateProvider).user;
  if (user == null) {
    throw Exception('User must be logged in to access emergency contacts');
  }

  return FirebaseFirestore.instance
      .collection('emergency_contacts')
      .where('userId', isEqualTo: user.id)
      .orderBy('isPrimary', descending: true)
      .orderBy('name')
      .snapshots()
      .map((snapshot) => snapshot.docs
          .map(
              (doc) => EmergencyContact.fromJson({...doc.data(), 'id': doc.id}))
          .toList());
});

final userAlertsProvider = StreamProvider<List<SafetyAlert>>((ref) {
  final user = ref.watch(authStateProvider).user;
  if (user == null) {
    throw Exception('User must be logged in to access safety alerts');
  }

  return FirebaseFirestore.instance
      .collection('safety_alerts')
      .where('userId', isEqualTo: user.id)
      .orderBy('createdAt', descending: true)
      .snapshots()
      .map((snapshot) => snapshot.docs
          .map((doc) => SafetyAlert.fromJson({...doc.data(), 'id': doc.id}))
          .toList());
});
